const { logger } = require("../utils");
const { ObjectId } = require("mongodb");

/**
 * UserAnalytics Service
 * Provides analytics data at the user level
 */
module.exports = async function ({ Services, config }) {
    // Helper function to get analytics for a single user
    const getUserAnalyticsData = async (user, options) => {
        const { startDate, endDate } = options;
        const db = await Services.MongoHelper.getDatabaseConnection();
        const employeecode = user.employeecode;

        const userData = {
            name: user.name || user.username || "Unknown",
            pcode: employeecode,
            loginCount: 0,
            customersCreated: 0,
            quotesCreated: 0,
            totalQuoteValue: 0,
            salesOrdersCreated: 0,
            totalSalesOrderValue: 0
        };

        // Build date filter for queries
        const dateFilter = {};
        if (startDate) {
            dateFilter.$gte = startDate;
        }
        if (endDate) {
            dateFilter.$lte = endDate;
        }

        // Count login events
        const loginQuery = {
            employeecode,
            event: "login"
        };

        // Add date filter if provided
        if (Object.keys(dateFilter).length > 0) {
            loginQuery.timestamp = dateFilter;
        }

        const loginEvents = await db.collection("loginLogs").countDocuments(loginQuery);
        userData.loginCount = loginEvents;

        // Count customers created by this user
        const customersQuery = {
            salesRepPID: employeecode
        };

        // Add date filter if provided
        if (Object.keys(dateFilter).length > 0) {
            customersQuery.createdAt = dateFilter;
        }

        const customersCreated = await db.collection("customers").countDocuments(customersQuery);
        userData.customersCreated = customersCreated;

        // Get quotes created by this user
        const quotesQuery = {
            salesRepPID: employeecode
        };

        // Add date filter if provided
        if (Object.keys(dateFilter).length > 0) {
            quotesQuery.createdAt = dateFilter;
        }

        const quotes = await db.collection("quotes").find(quotesQuery).toArray();

        userData.quotesCreated = quotes.length;

        // Calculate total quote value
        let totalQuoteValue = 0;
        quotes.forEach(quote => {
            // Use grand_total_WITH_DISCOUNT if available, otherwise use grand_total_WITHOUT_DISCOUNT
            const quoteValue = parseFloat(quote.grand_total_WITH_DISCOUNT) ||
                              parseFloat(quote.grand_total_WITHOUT_DISCOUNT) || 0;
            totalQuoteValue += quoteValue;
        });
        userData.totalQuoteValue = totalQuoteValue;

        // Get all quotes with their Salesforce IDs
        const quotesWithSalesCRMIDQuery = {
            salesRepPID: employeecode,
            quoteSalesCRMID: { $exists: true, $ne: null }
        };

        // Add date filter if provided
        if (Object.keys(dateFilter).length > 0) {
            quotesWithSalesCRMIDQuery.createdAt = dateFilter;
        }

        const allQuotesWithSalesCRMID = await db.collection("quotes")
            .find(quotesWithSalesCRMIDQuery)
            .toArray();

        // Extract the zoho sales order IDs
        const userSalesOrderIDs = allQuotesWithSalesCRMID.map(q => q.salesOrderId);

        // Get sales orders that reference this user's quotes
        const salesOrdersQuery = {
            salesorder_id: { $in: userSalesOrderIDs }
        };

        // Add date filter if provided
        if (Object.keys(dateFilter).length > 0) {
            salesOrdersQuery.created_time = dateFilter;
        }
        console.log(salesOrdersQuery);

        const userSalesOrders = await db.collection("salesOrders").find(salesOrdersQuery).toArray();

        userData.salesOrdersCreated = userSalesOrders.length;

        // Calculate total sales order value
        let totalSalesOrderValue = 0;
        userSalesOrders.forEach(so => {
            // Try different possible field names for the total value
            const soValue = parseFloat(so.total) ||
                           parseFloat(so.total_amount) ||
                           parseFloat(so.sub_total) ||
                           parseFloat(so.balance) || 0;
            totalSalesOrderValue += soValue;
        });
        userData.totalSalesOrderValue = totalSalesOrderValue;

        return userData;
    };

    return {
        /**
         * Get user-level analytics data
         * @param {Object} options - Query options
         * @param {Date} [options.startDate] - Start date for filtering data
         * @param {Date} [options.endDate] - End date for filtering data
         * @param {string} [options.employeecode] - Employee code to filter by specific user
         * @returns {Object} User-level analytics data
         */
        getUserAnalytics: async (options = {}) => {
            try {
                const db = await Services.MongoHelper.getDatabaseConnection();

                // If employeecode is provided, get analytics for just that user
                if (options.employeecode) {
                    const user = await db.collection("salesPersons").findOne({ employeecode: options.employeecode });
                    if (!user) {
                        return { ok: false, message: "User not found" };
                    }

                    const userData = await getUserAnalyticsData(user, options);
                    return { ok: true, data: [userData] };
                }

                // Otherwise, get analytics for all users
                const salesPersons = await db.collection("salesPersons").find({}).toArray();

                // Initialize results array
                const results = [];

                // Process each sales person
                for (const user of salesPersons) {
                    if (!user.employeecode) continue; // Skip users without employee code

                    const userData = await getUserAnalyticsData(user, options);
                    results.push(userData);
                }

                return { ok: true, data: results };
            } catch (error) {
                logger.error("Error getting user analytics:", error);
                return { ok: false, message: error.message };
            }
        },

        /**
         * Get analytics for a specific user
         * @param {Object} options - Query options
         * @param {string} options.employeecode - Employee code of the user
         * @param {Date} [options.startDate] - Start date for filtering data
         * @param {Date} [options.endDate] - End date for filtering data
         * @returns {Object} User analytics data
         */
        getUserAnalyticsByEmployeeCode: async (options) => {
            if (!options.employeecode) {
                return { ok: false, message: "Employee code is required" };
            }

            return await Services.UserAnalytics.getUserAnalytics(options);
        }
    };
};
