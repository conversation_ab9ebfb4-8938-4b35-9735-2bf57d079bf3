{"name": "grogu", "version": "1.0.0", "description": "Youngling express boilerplate just like <PERSON><PERSON><PERSON>!", "main": "app.js", "scripts": {"test": "node initTest.js", "dev": "node app dev", "start": "node app"}, "repository": {"type": "git", "url": "git+https://github.com/rohand<PERSON><PERSON><PERSON>kar/grogu.js.git"}, "author": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "license": "ISC", "bugs": {"url": "https://github.com/rohand<PERSON><PERSON><PERSON><PERSON>/grogu.js/issues"}, "homepage": "https://github.com/rohandhamap<PERSON>kar/grogu.js#readme", "dependencies": {"@aws-sdk/client-s3": "^3.686.0", "@aws-sdk/s3-request-presigner": "^3.686.0", "@sendgrid/mail": "^8.1.4", "axios": "^1.7.2", "body-parser": "^1.19.0", "compression": "^1.7.4", "cors": "^2.8.5", "cron": "^3.1.7", "dotenv": "^8.2.0", "exceljs": "^4.4.0", "express": "^4.17.1", "form-data": "^4.0.2", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "mime-types": "^2.1.35", "mongodb": "^6.8.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-base64-image": "^2.0.7", "nodemailer": "^6.9.16", "sharp": "^0.33.5", "ssh2-sftp-client": "^11.0.0"}, "devDependencies": {"babel-eslint": "^10.1.0", "chai": "^4.2.0", "chai-http": "^4.3.0", "eslint": "^7.17.0", "mocha": "^10.5.2"}}