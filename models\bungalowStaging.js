const { ObjectId } = require("mongodb");

module.exports = {
    collections:[
        "customers",
        "sessions",
        "products",
        "holdRequests",
        "wishlists",
        "addresses",
        "carts",
        "quotes",
        "salesOrders",
        "analyticsExcels",
        "cronSchedules",
        "customerFeedbacks",
        "loginLogs",
        "miscellaneous",
        "notes",
        "oidcs",
        "productEvents",
        "quotesShareHistory",
        "salesPersons",
        "stateDescriptions",
        "statePincode",
    ],
    // schema for information only
    schema:{
        customers:{
            _id: "ObjectId (primary key)",
			name: "string",
			email: "string",
			address: "string",
			mobile: "string",
			createdAt: "date",
			updatedAt: "date",
            isArchitect: "Boolean", // by default false
        }
    }
}