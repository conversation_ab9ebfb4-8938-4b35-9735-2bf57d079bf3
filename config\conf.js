/**
 * conf.js
 * this is the main config file and can be accessed through the "config" dependency
 * which is injected in both controllers and middlewares
 */
module.exports = {
    env: process.env.ENV,
	mongo: {
		un: process.env.MONGODB_USERNAME,
		pw: process.env.MONGODB_PASSWORD,
		host: process.env.MONGODB_HOST,
		dbName: process.env.MONGODB_DATABASE_NAME,
		dnsSeedList: process.env.MONGODB_DNS_SEED_LIST == "true" ? true : false
	},
	foyrAuth: {
		clientId: process.env.CLIENT_ID,
		clientSecret: process.env.CLIENT_SECRET,
		redirectUri: process.env.REDIRECT_URI,
		authScope: process.env.AUTH_SCOPE,
	},
	sf: {
		baseUrl: process.env.SF_BASE_URL,
		token: process.env.SF_TOKEN
	},
	zoho: {
		accountsBaseUrl: process.env.ZOHO_ACCOUNTS_BASE_URL,
		booksBaseUrl: process.env.ZOHO_BOOKS_BASE_URL,
		sales_order_token: "",
		organization_id: process.env.ZOHO_ORGANIZATION_ID
	},
	pim: {
		host: process.env.PIM_HOST,
		port: process.env.PIM_PORT,
		username: process.env.PIM_USERNAME,
		password: process.env.PIM_PASSWORD,
	},
	ap: {
		baseUrl: process.env.AP_BASE_URL,
		jwtSecret: process.env.JWT_SECRET
	},
	aws: {
		bucket: process.env.BUCKET_NAME,
		accessKeyId: process.env.ACCESS_KEY_ID,
		secretAccessKey: process.env.SECRET_ACCESS_KEY,
		region: process.env.REGION
	},
	FRONTEND_URL: process.env.ENV === "staging" 
    ? "https://apbungalow.foyr.com" 
    : "https://anthology.foyr.com",
	helo: {
		userName: process.env.HELO_USER_NAME,
		password: process.env.HELO_USER_PASSWORD,
		apiKey: process.env.HELO_API_KEY,
		token: "",
		heloMobile: process.env.HELO_FROM_MOBILE,
	},
	// SENDGRID_KEY: process.env.SENDGRID_API_KEY,
	SMTP_FROM_EMAIL: process.env.SMTP_FROM_EMAIL,
	SMTP_HOST: process.env.SMTP_HOST,
	SMTP_PORT: process.env.SMTP_PORT,
	SMTP_USER: process.env.SMTP_USER,
	SMTP_PASS: process.env.SMTP_PASS
};
