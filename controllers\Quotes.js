const { logger } = require("../utils");

module.exports.routes = function ({ Services, config }) {
	return {
		/**
		 * Approval Status Update
		 * This route is used to update the approval status of a quote used by sf
		 */
		"POST /approval-status": {
			version: "v1.0",
			handler: async (req, res) => {
				try {
					// Extract required parameters from the request body
					const { quoteSalesCRMID, quoteId, status } = req.body;

					// Call the approvalStatus service with extracted parameters
					const result = await Services.Quotes.approvalStatus({ quoteId, status, quoteSalesCRMID });

					// Return a successful response with the result
					return res.json({ ok: true, message: result.message, data: result.data });
				} catch (error) {
					// Log any errors that occur during the process
					logger.error(error);

					// Return an error response
					return res.json({ ok: false, message: error.message });
				}
			},
		},

		/**
		 * Create Quote v1
		 * This route is used to create quote from wishlist or mto flow
		 */
		"POST /create-quote": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					const {
						customerCRMID,
						addressCRMID,
						architectCRMID = "",
						category,
						subCategory,
						itemList,
						salesRepPID,
						wishlistID_cartID,
						DISCOUNT_PERCENTAGE,
						discount_AMOUNT,
						grand_total_WITHOUT_DISCOUNT,
						grand_total_WITH_DISCOUNT,
					} = req.body;
					// result returns the response from the salesforce
					const result = await Services.Quotes.createQuote({
						customerCRMID,
						addressCRMID,
						architectCRMID,
						category,
						subCategory,
						itemList,
						salesRepPID,
						wishlistID_cartID,
						DISCOUNT_PERCENTAGE,
						discount_AMOUNT,
						grand_total_WITHOUT_DISCOUNT,
						grand_total_WITH_DISCOUNT,
					});
					return res.json({ ok: true, message: result.message, data: result.data });
				} catch (error) {
					logger.error(error);
					return res.json({ ok: false, message: error.message });
				}
			},
		},

		/**
		 * Create Quote from cart
		 * This route is used to create quote specially from cart
		 */
		"POST /create-quote-from-cart": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					const {
						customerCRMID,
						addressCRMID,
						architectCRMID = "",
						category,
						itemList,
						salesRepPID,
						cartCRMID,
						DISCOUNT_PERCENTAGE,
						discount_AMOUNT,
						grand_total_WITHOUT_DISCOUNT,
						grand_total_WITH_DISCOUNT,
						comments,
						QUOTETYPE,
						quoteURL,
						payment_terms,
						showDiscount = false,
					} = req.body;

					// check if the quote already exists with the same cartCRMID and categorySubCategory.
					const existingQuote = await Services.Quotes.getQuoteByCartCRMIDAndCategorySubCategory({
						cartCRMID,
						category,
					});
					// if the quote exists, then update the quote to isActive = false and create a new quote.
					if (existingQuote.ok) {
						const result = await Services.Quotes.updateQuoteByQuery({
							query: { quoteCRMID: existingQuote.data[0].quoteCRMID },
							update: { isActive: false, showDiscount },
						});
						// logger.debug(result);
					}
					// logger.debug(existingQuote.data);
					// set the correct version of the quote.
					const quoteVersion =
						existingQuote.ok && existingQuote.data.length > 0
							? (existingQuote.data[0].quoteVersion || 0) + 1
							: 1;
					// logger.debug(quoteVersion);
					// result returns the response from the salesforce
					const result = await Services.Quotes.createQuotev2({
						customerCRMID,
						addressCRMID,
						architectCRMID,
						category,
						itemList,
						salesRepPID,
						wishlistID_cartID: cartCRMID,
						quoteVersion,
						DISCOUNT_PERCENTAGE,
						discount_AMOUNT,
						grand_total_WITHOUT_DISCOUNT,
						grand_total_WITH_DISCOUNT,
						comments,
						QUOTETYPE,
						type: "cart",
						quoteURL,
						payment_terms,
						showDiscount,
					});
					if (result.ok === false) {
						return res.json({ ok: false, message: result.message });
					}
					return res.json({ ok: true, message: result.message, data: result.data });
				} catch (error) {
					logger.error(error);
					return res.json({ ok: false, message: error.message });
				}
			},
		},

		/**
		 * Retrieve Quote Details
		 * This route is used to retrieve quote details
		 */
		"POST /": {
			version: "v1.0",
			handler: async (req, res) => {
				try {
					// Extract quote details from request body
					const { quoteId, quoteCRMID, customerCRMID, type, price, quoteType } = req.body;

					// Fetch quote details using the Quotes service
					const result = await Services.Quotes.getQuote({
						quoteId,
						quoteCRMID,
						customerCRMID,
						type,
						price,
						quoteType,
					});

					// Return successful response with quote data
					return res.json({ ok: true, message: result.message, data: result.data });
				} catch (error) {
					// Log any errors that occur during the process
					logger.error(error);

					// Return error response
					return res.json({ ok: false, message: error.message });
				}
			},
		},

		/**
		 * Create Sales Order In ZOHO
		 * This route Creates Sales order in zoho then update the sf about it.
		 */
		"POST /zoho-sales-order-create": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					// Extract required fields from request body
					const {
						customerCRMID,
						line_items,
						quoteCRMIDs,
						custom_fields,
						totalAmount,
						payment_terms = 0,
					} = req.body;

					// Get database connection
					const db = await Services.MongoHelper.getDatabaseConnection();

					// Check stock availability for all items

					const checkItems = line_items.map((item) => item.SKU);
					const products = await db
						.collection("products")
						.find({ code: { $in: checkItems } })
						.toArray();

					const quote = await db.collection("quotes").findOne({ quoteCRMID: quoteCRMIDs[0] });
					if (quote.QUOTETYPE === "MTS") {
						// Filter out items where stock is less than requested quantity
						const stockUnavailable = line_items
							.filter((item) => {
								const product = products.find((p) => p.code === item.SKU);
								return Number(product.stock) < Number(item.quantity);
							})
							.map((item) => item.sku);

						// Return error if any items are out of stock
						if (stockUnavailable.length > 0) {
							return res.json({
								ok: false,
								message: `Stock unavailable for the following items: ${stockUnavailable.join(", ")}`,
							});
						}
					}

					// Get customer details from database
					const customer = await db.collection("customers").findOne({ customerCRMID });
					let zohoCustomerId;

					// Check if customer exists in Zoho
					const mobileWithoutPlus = customer.mobile.replace(/^\+/, "");
					const zohoCustomer = await Services.ZohoHelper.contactSearch({ mobile: mobileWithoutPlus });

					// let shipping_address_id = zohoCustomer?.contacts[0]?.custom_fields[1]?.field_id;
					// let billing_address_id = zohoCustomer?.contacts[0]?.custom_fields[1]?.field_id;
					// logger.debug(JSON.stringify(zohoCustomer));

					if (zohoCustomer.contacts.length > 0) {
						// Use existing Zoho customer ID if found
						zohoCustomerId = zohoCustomer.contacts[0].contact_id;
						logger.info(`Existing Zoho customer found : ${JSON.stringify(zohoCustomer)}`);
					} else {
						// Create new Zoho contact if customer doesn't exist
						const newZohoCustomer = await Services.ZohoHelper.createContactDuringRegistration({
							name: customer.name,
							email: customer.email,
							mobile: customer.mobile,
							isExportCustomer: customer?.isExportCustomer || false,
						});
						zohoCustomerId = newZohoCustomer.contact.contact_id;
						// shipping_address_id = newZohoCustomer.contact.billing_address.address_id;
						// billing_address_id = newZohoCustomer.contact.billing_address.address_id;
						logger.info(`New Zoho customer created with ID: ${zohoCustomerId}`);

						// Update local database with new Zoho customer ID
						await db
							.collection("customers")
							.updateOne({ customerCRMID }, { $set: { customer_id: zohoCustomerId } });
						logger.info(`Updated local customer record with Zoho ID: ${zohoCustomerId}`);
					}

					// Get customer's primary shipping address
					const customerAddress = await db.collection("addresses").findOne({
						customerId: customer._id.toString(),
						primaryAddress: true,
					});

					// Return error if no primary address found
					if (!customerAddress) {
						return res.json({
							ok: false,
							message: "No primary address found for customer",
						});
					}

					// Check if customer is export customer
					const isExportCustomer = customer?.isExportCustomer || false;

					// Process each line item to add appropriate tax details
					const updatedItemList = line_items.map((item) => {
						const product = products.find((p) => p.code === item.SKU);
						let tax_id = "";

						// For export customers, no tax should be applied
						if (isExportCustomer) {
							config.env === "staging" ? tax_id = "1846261000000024287" : tax_id = "2026911000000027292"; // No tax for export customers
						} else {
							// Set appropriate tax ID based on customer region for domestic customers
							if (customerAddress.region === "MH") {
								logger.debug("intra state");
								const intraStateTax = product.item_tax_preferences.find(
									(tax) => tax.tax_specification === "intra"
								);
								tax_id = intraStateTax?.tax_id || "";
							} else {
								const interStateTax = product.item_tax_preferences.find(
									(tax) => tax.tax_specification === "inter"
								);
								tax_id = interStateTax?.tax_id || "";
							}
						}

						// Return item with all required fields
						return {
							SKU: item.SKU,
							rate: item.rate,
							name: item.description,
							description: "",
							quantity: item.quantity,
							discounts: item.discounts,
							tax_id,
							tags: product.tags || [],
							tax_exemption_code: product.tax_exemption_code || "",
							item_custom_fields: product.item_custom_fields || [],
							hsn_or_sac: product.hsn_or_sac || "",
							warehouse_id: item.warehouse_id || "",
							gst_treatment_code: product.gst_treatment_code || "",
							project_id: product.project_id || "",
							unit: product.unit || "",
							item_id: product.item_id || "",
						};
					});

					// get the zohoStateCode from the stateDescription collection and use it to set the place_of_supply field in the SO payload
					const stateDescription = await db
						.collection("stateDescriptions")
						.findOne({ Region: customerAddress.region });

					// Prepare sales order payload for Zoho
					const soPayload = {
						customer_id: zohoCustomerId,
						line_items: updatedItemList,
						custom_fields,
						totalAmount,
						payment_terms,
						quoteCRMIDs,
						products,
						place_of_supply: isExportCustomer ? "": stateDescription.ZohoStateCode,
						// shipping_address_id,
						// billing_address_id,
					};

					// Create sales order in Zoho
					const zohoOrder = await Services.ZohoHelper.createSalesOrder(soPayload);
					logger.info(`Zoho sales order created successfully: ${zohoOrder.salesorder.salesorder_id}`);

					// add quoteCRMIDS in the SO response
					zohoOrder.salesorder.quoteCRMIDS = quoteCRMIDs;
					// Save sales order to local database
					await db.collection("salesOrders").insertOne(zohoOrder.salesorder);
					// Return success response
					res.json({
						ok: true,
						message: `Sales order created successfully with order number: ${zohoOrder.salesorder.salesorder_number}`,
					});

					// Update order status in Salesforce for all related quotes
					const quotes = await db
						.collection("quotes")
						.find({ quoteCRMID: { $in: quoteCRMIDs } })
						.toArray();
					quotes.forEach(async (quote) => {
						const sfres2 = await Services.SalesforceHelper.orderStatusUpdate({
							categorySalesCRMID: quote.categorySalesCRMID,
							quoteCRMID: quote.quoteCRMID,
							status: "PROCEED TO CHECK OUT",
						});
						logger.debug({ sfres2: JSON.stringify(sfres2) });
					});

					// Update quote status in local database
					db.collection("quotes").updateMany(
						{ quoteCRMID: { $in: quoteCRMIDs } },
						{ $set: { status: "ORDER_CREATED", salesOrderId: zohoOrder.salesorder.salesorder_id } }
					);

					// Update product stock levels
					line_items.forEach(async (item) => {
						Services.ZohoHelper.updateProductStock({ sku: item.SKU });
					});

					// Remove ordered items from customer's cart
					await db.collection("carts").updateOne(
						{ customerCRMID },
						{
							$pull: { itemList: { productName: { $in: line_items.map((item) => item.SKU) } } },
							$set: { comments: {} },
						}
					);

					// Remove any hold requests for ordered items
					const holdRequests = await db.collection("holdRequests").find({ customerCRMID }).toArray();
					holdRequests.forEach(async (request) => {
						if (line_items.map((item) => item.SKU).includes(request.skuCode)) {
							await Services.SalesforceHelper.unholdRequest({
								holdCRMID: request.holdCRMID,
								salesRepPID,
							});
						}
					});
				} catch (error) {
					// Log error and return error response
					logger.error(error);
					return res.json({ ok: false, message: error.message });
				}
			},
		},

		/**
		 * Get Sales Order list for a particular user.
		 * Retrieves a list of Sales Orders for a specific customer from Zoho.
		 */
		"GET /sales-order-details": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					// Establish database connection
					const db = await Services.MongoHelper.getDatabaseConnection();

					// Extract query parameters
					let { mobile, sort_column, sort_order } = req.query;
					logger.debug(`Fetching sales orders for mobile: ${mobile}`);

					// Fetch customer details using the provided mobile number
					const customer = await Services.Customers.getCustomer({ mobile: mobile.trim() });
					if (!customer.ok) {
						return res.json({ ok: false, message: `No customer found for mobile: ${mobile}` });
					}

					// Fetch sales order details from Zoho using customer ID
					const soResponse = await Services.ZohoHelper.getSalesOrderFromZoho({
						customer_id: customer.data[0].customer_id,
						sort_column,
						sort_order,
					});

					if (!soResponse.ok) {
						return res.json({ ok: false, message: soResponse.message });
					}

					// Return successful response with sales order data
					res.json({
						ok: true,
						message: "Sales order details fetched successfully",
						data: soResponse.data.salesorders,
					});
				} catch (error) {
					// Log and return error response
					logger.error(`Error fetching sales order details: ${error.message}`);
					res.json({ ok: false, message: error.message });
				}
			},
		},

		/**
		 * Retrieves sales details for a specific Sales Order.
		 * This route gives all details related to an SO.
		 */
		"GET /sales-order-details/:salesorder_id": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"], // Middleware to ensure the user is logged in
			handler: async (req, res) => {
				try {
					// Extract sales order ID from request parameters
					const { salesorder_id } = req.params;
					logger.debug(`Fetching sales order details for SO ID: ${salesorder_id}`);

					// Fetch sales order details from Zoho
					const { ok, data, message } = await Services.ZohoHelper.getSalesOrderFromZoho({ salesorder_id });

					// Handle error response from Zoho API
					if (!ok) {
						return res.json({
							ok: false,
							message: data?.message ? data.message : message, // Return API error message if available
						});
					}

					// Extract SKUs from line items in the sales order
					const skus = data.salesorder.line_items.map((item) => item.sku);

					// Query to fetch product details including base64 images based on SKUs
					const query = [
						{
							$match: { code: { $in: skus } }, // Match products where code exists in the sales order SKUs
						},
						{
							$project: {
								_id: 0, // Exclude MongoDB _id field
								code: 1, // Include product code
								base64Image: 1, // Include base64 image of the product
							},
						},
					];

					// Fetch product details from the database
					const products = await Services.Products.getProductsByQuery({ query });

					// Attach the base64 image to the corresponding line items in the sales order
					data.salesorder.line_items.forEach((item) => {
						const product = products.find((product) => product.code === item.sku);
						if (product) {
							item.base64Image = product.base64Image;
						}
					});

					// Return the sales order details with base64 images attached
					return res.json({
						ok: true,
						message: "Sales order details fetched successfully",
						data: data.salesorder,
					});
				} catch (error) {
					// Log the error and send an appropriate response
					logger.error(`Error fetching sales order details: ${error.message}`);
					return res.json({ ok: false, message: error.message });
				}
			},
		},

		/**
		 * Get Sales Order Details for architect
		 * This route is used to get sales order details for a particular architect.
		 */
		"GET /sales-order-details-for-architect": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					const { architectCRMID, pageNo, pageSize } = req.query;
					logger.debug(pageNo, pageSize, architectCRMID);
					if (pageNo < 1 || pageSize < 1) {
						return res.json({ ok: false, message: "Invalid page number or page size" });
					}
					const response = await Services.Quotes.getSalesOrderDetailsForArchitect({
						architectCRMID,
						pageNo,
						pageSize,
					});
					res.json({
						ok: true,
						message: "Sales order details fetched successfully",
						data: response.salesOrders,
						count: response.salesOrderCount,
					});
				} catch (error) {
					logger.error(error);
					res.json({ ok: false, message: error.message });
				}
			},
		},

		/**
		 * Get Sales Order Details for Sales Representative
		 * This route is used to get sales order details for a particular salesRep.
		 */
		"GET /sales-order-details-seller": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					const { employeecode, pageNo, pageSize } = req.query;
					if (pageNo < 1 || pageSize < 1) {
						return res.json({ ok: false, message: "Invalid page number or page size" });
					}
					const response = await Services.ZohoHelper.getSalesOrderFromZoho({
						SalesRepPID: employeecode,
					});
					res.json({
						ok: true,
						message: "Sales order details fetched successfully",
						data: response.data.salesorders,
					});
				} catch (error) {
					logger.error(error);
					res.json({ ok: false, message: error.message });
				}
			},
		},

		/**
		 * Convert Excel to JSON for MTO Quote
		 *
		 * @route POST /excel-to-json
		 * @version v1.0
		 * @middleware uploadExcel - Handles file upload
		 *
		 * This route converts an uploaded Excel file to JSON format for MTO Quote processing.
		 */
		"POST /excel-to-json": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn", "uploadExcel"],
			handler: async (req, res) => {
				// Check if a file was uploaded
				if (!req.file) {
					return res
						.status(400)
						.json({ ok: false, message: "No file uploaded. Please upload an Excel file." });
				}

				try {
					// Convert the Excel file to JSON
					const data = await Services.Quotes.excelToJson(req.file.buffer);

					// Send the JSON data as response
					res.json({
						ok: true,
						message: "Excel data converted to JSON successfully",
						data,
					});
				} catch (error) {
					logger.error("Error converting Excel to JSON:", error);
					res.status(500).json({
						ok: false,
						message:
							"An error occurred while processing the Excel file. Please ensure the file is in the correct format and try again.",
					});
				}
			},
		},
		/**
		 * Convert Excel to JSON for MTO Quote
		 *
		 * @route POST /excel-to-json v2
		 * @version v1.0
		 * @middleware uploadExcel - Handles file upload
		 *
		 * This route converts an uploaded Excel file to JSON format for MTO Quote processing.
		 */
		"POST v2.0 /excel-to-json": {
			version: "v2.0",
			localMiddlewares: ["isLoggedIn", "uploadExcel"],
			handler: async (req, res) => {
				// Check if a file was uploaded
				if (!req.file) {
					return res
						.status(400)
						.json({ ok: false, message: "No file uploaded. Please upload an Excel file." });
				}

				try {
					// Convert the Excel file to JSON
					const data = await Services.Quotes.excelToJsonV2(req.file.buffer);

					// Send the JSON data as response
					res.json(data);
				} catch (error) {
					logger.error("Error converting Excel to JSON:", error);
					res.status(500).json({
						ok: false,
						message: error.message,
					});
				}
			},
		},

		/**
		 * Get Signed URL for Quote Document
		 *
		 * @route GET /get-signed-url
		 * @version v1.0
		 * @param {string} key - The S3 object key for the quote document
		 *
		 * This route generates a signed URL for accessing a quote document stored in S3
		 */
		"GET /get-signed-url": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					const { fileName, folderName } = req.query;

					if (!fileName) {
						return res.status(400).json({
							ok: false,
							message: "Document fileName is required",
						});
					}

					const result = await Services.AWSHelpers.getSignedUrlFromS3({ fileName, folderName });

					if (!result.ok) {
						return res.status(500).json({
							ok: false,
							message: "Failed to generate signed URL",
							error: result.error,
						});
					}

					res.json({
						ok: true,
						message: "Signed URL generated successfully",
						url: result.signedUrl,
						fileUrl: result.publicUrl,
					});
				} catch (error) {
					logger.error("Error generating signed URL:", error);
					res.status(500).json({
						ok: false,
						message: "An error occurred while generating the signed URL",
					});
				}
			},
		},

		/**
		 * Update quote pdf link based on approval status
		 *
		 * @route PUT /
		 * @version v1.0
		 * @param {string} status - The approval status of the quote (approved or pending)
		 * @param {string} fileUrl - The url of the PDF file uploaded to s3
		 *
		 * This route generates a signed URL for accessing a quote document stored in S3
		 */

		"PUT /": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async (req, res) => {
				try {
					const { status, fileUrl, quoteCRMID, disabled } = req.body;

					const result = await Services.Quotes.updatePdfUrlAndStatus({ status, fileUrl, quoteCRMID, disabled });

					if (!result.ok) {
						return res.status(500).json({
							ok: false,
							message: "Failed to update Quote with PDF url",
							error: result.error,
						});
					}

					res.json({
						ok: true,
						message: "Pdf added in quote successfully",
					});
				} catch (error) {
					logger.error("Error saving fileUrl in quote", error);
					res.status(500).json({
						ok: false,
						message: "An error occurred while saving fileurl",
					});
				}
			},
		},
	};
};
