/**
 * Share Controller
 * This module handles all share routes and operations via email and whatsapp
 */

// Import the logger utility for logging purposes
const { logger } = require("../utils");
const axios = require("axios");
// const sgMail = require("@sendgrid/mail");
const nodemailer = require("nodemailer");
const emailHelper = require("../helpers/email");
// const { SelectObjectContentEventStreamFilterSensitiveLog } = require("@aws-sdk/client-s3");
// const { generateWhatsappToken } = require("../CronScripts/CronJobs");
const { ObjectId } = require("mongodb");
const FormData = require("form-data")

module.exports.routes = function ({ Services, config }) {
	return {
		/**
		 * Send WhatsApp Message
		 * This route sends a WhatsApp message to the specified mobile number
		 *
		 * @route POST /whatsapp
		 * @param {string} mobile - 10 digit mobile number to send message to
		 * @param {string} link - Link to be shared in the message
		 * @returns {object} Response from WhatsApp API or error message
		 */
		"POST /whatsapp": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async function (req, res) {
				try {
					const db = Services.MongoHelper.getDatabaseConnection();
					// Generate whatsapp token while sending whatsapp message
					await Services.CronJobs.generateWhatsappToken();
					// Extract mobile and link from request body
					const { mobile, quoteId, shareto } = req.body;
					let user;
					if (shareto === "customer" || shareto === "architect") {
						user = await db
							.collection("customers")
							.findOne({ mobile: { $regex: mobile.toString().replace(/^\+/, ""), $options: "i" } });
					} else if (shareto === "seller") {
						user = await db
							.collection("salesPersons")
							.findOne({ mobile: { $regex: mobile.toString().replace(/^\+/, ""), $options: "i" } });
					} else {
						return res.json({ ok: false, message: "Invalid shareto field" });
					}

					if (!user) {
						return res.json({ ok: false, message: `${shareto} not found with this mobile number.` });
					}

					// Validate required parameters
					if (!mobile || !quoteId) {
						return res.json({ ok: false, message: "Mobile number and quoteId are required" });
					}

					// Set up headers for WhatsApp API request
					let headers = {
						Authorization: `Bearer ${config.helo.token}`,
						"Content-Type": "application/json",
					};

					// prepare the data
					const data = {
						messaging_product: "whatsapp",
						recipient_type: "individual",
						check_consent: false,
						to: `${mobile}`,
						from: `${config.helo.heloMobile}`,
						type: "template",
						template: {
							name: config.env === "staging" ? "apbungalow_quotes" : "bungalow_quotes_final",
							category: "UTILITY",
							language: {
								code: "en",
							},
							components: [
								{
									type: "header",
									parameters: [
										{
											type: "text",
											text: `${user?.name || "Customer"}`,
										},
									],
								},
								{
									type: "body",
									parameters: [
										{
											type: "text",
											text: `${quoteId}`,
										},
									],
								},
							],
						},
					};

					// Make API call to WhatsApp service
					const response = await axios({
						method: "post",
						url: "https://wabaapp.helo.ai/messages/single",
						headers: headers,
						data: data,
					});

					// Check if the message was successfully sent
					if (response.data.data.status === true) {
						// Save user info along with timestamp in quotesShareHistory
						await db.collection("quotesShareHistory").insertOne({
							mobile,
							quoteId,
							shareto,
							userName: user?.name || "Unknown",
							userId: user?._id || null,
							sharedAt: new Date(), // Current timestamp
						});
					} else {
						// If the API response indicates failure, return an error response
						return res.status(400).json({ ok: false, message: response.data.data.message });
					}

					// Return successful API response
					res.json(response.data);
				} catch (e) {
					// Log error and return generic error message
					logger.error(e);
					res.json({ ok: false, message: e.message });
				}
			},
		},
		/**
		 * Send Email Messages
		 * This route sends a email message to the specified mobile number
		 *
		 * @route POST /email
		 * @param {string} email - valid email address
		 * @param {string} quoteId - quoteId of the quote you want to share
		 * @returns {object} Response from email API or error message
		 */
		"POST /email": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async function (req, res) {
				try {
					// Extract email and quoteId from request body
					const { email, quoteId } = req.body;

					// Validate required parameters
					if (!email || !quoteId) {
						return res.json({ ok: false, message: "email and quoteId are required" });
					}

					// get the customer
					const db = Services.MongoHelper.getDatabaseConnection();
					const user = await db.collection("customers").findOne({ email });

					// validate the user email
					if (user === null) {
						res.status(400).json({ ok: false, message: "Customer is not registered to this email." });
					}

					// prepare email body
					const html = await emailHelper(user, quoteId, config);

					// Create SMTP transporter
					const transporter = nodemailer.createTransport({
						host: config.SMTP_HOST,
						port: config.SMTP_PORT,
						secure: false, // false for TLS - port 587,
						auth: {
							user: config.SMTP_USER,
							pass: config.SMTP_PASS,
						},
						tls: {
							ciphers: "SSLv3",
						},
					});

					// prepare email options
					const mailOptions = {
						from: {
							name: "ASIAN PAINTS",
							address: config.SMTP_FROM_EMAIL,
						},
						to: email,
						subject: "Here's the Quotation You Requested 🎉",
						html, // Using the same HTML template
					};

					// send email
					const info = await transporter.sendMail(mailOptions);

					if (!info.messageId) {
						return res.json({ ok: false, message: "Failed to send email" });
					}

					// Return successful response
					res.status(200).json({ ok: true, message: "Email Sent Successfully!" });
				} catch (e) {
					// Log error and return generic error message
					logger.error(e);
					res.json({ ok: false, message: e.message });
				}
			},
		},

		"POST /email": {
			version: "v2.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async function (req, res) {
				try {
					// Extract email and quoteId from request body
					const { email, quoteId, documentLink } = req.body;

					// Validate required parameters
					if (!email || !quoteId || !documentLink) {
						return res.json({ ok: false, message: "email, quoteId and documentLink are required" });
					}

					// get the database connection
					const db = Services.MongoHelper.getDatabaseConnection();

					// get the customer
					const user = await db.collection("customers").findOne({ email });

					// validate the user email
					if (user === null) {
						return res
							.status(400)
							.json({ ok: false, message: "Customer is not registered to this email." });
					}

					// fetch the quote document
					const quote = await db.collection("quotes").findOne({ _id: ObjectId.createFromHexString(quoteId) });

					// validate the quote
					if (!quote) {
						return res.status(404).json({ ok: false, message: "Quote not found" });
					}

					// check if PDF URL exists
					if (!quote.uploadedFile || !quote.uploadedFile.fileUrl) {
						return res.status(400).json({ ok: false, message: "No PDF file associated with this quote" });
					}

					// prepare email body
					const html = await emailHelper(user, quoteId, config);

					// Download the PDF from S3
					const pdfUrl = documentLink;
					const pdfResponse = await axios.get(pdfUrl, { responseType: "arraybuffer" });
					const pdfBuffer = Buffer.from(pdfResponse.data);

					// Extract filename from the URL or use a default name
					const fileName = documentLink.split("/").pop() || `Quotation-${quoteId}.pdf`;

					// Prepare form data for the email request
					let data = new FormData();
					data.append('reqTo', email);
					data.append('reqSubject', 'Nilaya Anthology Quotation As Requested 🎉');
					data.append('reqBody', html);
					data.append('reqFrom', config.SMTP_FROM_EMAIL);
					data.append('reqAttachment1', pdfBuffer, fileName);

					// Configure the request
					let reqBody = {
						method: 'post',
						maxBodyLength: Infinity,
						url: 'https://api.asianpaints.com/v2/triggerEmail',
						headers: { 
							...data.getHeaders()
						},
						data: data
					};

					// Send the email request
					const response = await axios.request(reqBody);
					console.log(response.data)

					// Check if the email was successfully sent
					if (response.data.SUCCESS !== "1") {
						return res.json({ ok: false, message: "Failed to Send Email" });
					}

					// Return successful response
					res.status(200).json({ ok: true, message: "Email Sent Successfully with Quotation PDF!" });
				} catch (e) {
					// Log error and return generic error message
					logger.error(e);
					res.status(500).json({ ok: false, message: e.message });
				}
			},
		},

		"POST /whatsapp/v2": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async function (req, res) {
				try {
					const db = Services.MongoHelper.getDatabaseConnection();

					// Generate WhatsApp token while sending the message
					await Services.CronJobs.generateWhatsappToken();

					// Extract request data
					const { mobile, quoteId, shareto, documentLink } = req.body;

					// Validate required parameters
					if (!mobile || !quoteId || !documentLink) {
						return res.json({
							ok: false,
							message: "Mobile number, quoteId, and document link are required",
						});
					}

					// Determine user type (customer, architect, or seller)
					let user;
					if (shareto === "customer" || shareto === "architect") {
						user = await db
							.collection("customers")
							.findOne({ mobile: { $regex: mobile.toString().replace(/^\+/, ""), $options: "i" } });
					} else if (shareto === "seller") {
						user = await db
							.collection("salesPersons")
							.findOne({ mobile: { $regex: mobile.toString().replace(/^\+/, ""), $options: "i" } });
					} else {
						return res.json({ ok: false, message: "Invalid shareto field" });
					}

					// If user not found, return error
					if (!user) {
						return res.json({ ok: false, message: `${shareto} not found with this mobile number.` });
					}

					// Set up headers for WhatsApp API request
					let headers = {
						Authorization: `Bearer ${config.helo.token}`,
						"Content-Type": "application/json",
					};

					// Prepare WhatsApp message payload (including PDF document)
					const data = {
						messaging_product: "whatsapp",
						recipient_type: "individual",
						check_consent: false,
						to: `${mobile}`,
						from: `${config.helo.heloMobile}`,
						type: "template",
						template: {
							name: "pdf_quote_testing", // New template name
							category: "UTILITY",
							language: { code: "en" },
							components: [
								{
									type: "header",
									parameters: [
										{
											type: "document",
											document: { link: documentLink }, // PDF document link
										},
									],
								},
								{
									type: "body",
									parameters: [
										{
											type: "text",
											text: `${user?.name || "Customer"}`,
										},
									],
								},
							],
						},
					};

					// Make API call to WhatsApp service
					const response = await axios.post("https://wabaapp.helo.ai/messages/single", data, { headers });

					// Check if the message was successfully sent
					if (response.data.data.status === true) {
						// Save user info along with timestamp in quotesShareHistory
						await db.collection("quotesShareHistory").insertOne({
							mobile,
							quoteId,
							shareto,
							documentLink, // Store PDF link
							userName: user?.name || "Unknown",
							userId: user?._id || null,
							sharedAt: new Date(),
						});
					} else {
						// If API response indicates failure, return an error response
						return res.status(400).json({ ok: false, message: response.data.data.message });
					}

					// Return successful API response
					res.json(response.data);
				} catch (e) {
					// Log error and return a generic error message
					logger.error(e);
					res.json({ ok: false, message: e.message });
				}
			},
		},
	};
};
