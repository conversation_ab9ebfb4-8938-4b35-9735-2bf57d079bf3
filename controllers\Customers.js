/**
 * Customers Controller
 * all Customers endpoints accessible to all will be here
 */

const { logger } = require("../utils");
const { mobileValidator, emailValidator } = require("../helpers/mobileEmailValidator");
const { ObjectId } = require("mongodb");
const { generateNonExistingMobileNumber } = require("./../helpers/mobileEmailValidator");

module.exports.routes = function ({ Services, config }) {
	return {
		/**
		 * Create Customer
		 * This route is used for creating a new customer or architect
		 */
		"POST /": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async function (req, res) {
				try {
					const { body } = req;

					// check if the mobile number has + as prefix if not then add it
					if (!body.mobile.toString().startsWith("+")) {
						body.mobile = `+${body.mobile}`;
					}

					// Check if the payload contains a code for anonymous customer creation
					if (body.code) {
						// Check if a customer exists with that code
						const existingCustomer = await Services.Customers.getCustomer({ code: body.code });

						if (existingCustomer.ok && existingCustomer.data.length > 0) {
							// Customer exists, start the session immediately
							const customer = existingCustomer.data[0]._id.toString();
							await Services.Customers.session({ customer: customer, isActive: true });
							return res.json({
								ok: true,
								message: "Session started for existing customer",
								data: { _id: customer },
							});
						} else {
							// Customer doesn't exist, create a new anonymous customer
							const createResult = await Services.Customers.create({
								...body,
								isAnonymous: true,
							});

							if (!createResult.ok) {
								return res.json({ ok: false, message: "Failed to create anonymous customer" });
							}

							const customer = createResult.re1.insertedId.toString();

							// Start the session for the new anonymous customer
							await Services.Customers.session({ customer: customer, isActive: true });

							return res.json({
								ok: true,
								message: "Anonymous customer created and session started",
								data: { _id: customer },
							});
						}
					}

					// Check if customer exists in Salesforce
					const sfCustomerExists = await Services.SalesforceHelper.fetchCustomer({
						email: body.email ? body.email : "",
						mobile: body.mobile ? body.mobile : "",
						customerCRMID: body.customerCRMID ? body.customerCRMID : "",
					});
					logger.debug(sfCustomerExists);

					// Check if customer exists locally
					const localCustomerExists = await Services.Customers.getCustomer({
						email: body.email,
						mobile: body.mobile,
						isArchitect: body?.isArchitect ? body.isArchitect : false,
					});

					// check if the architect exists in salesforce
					const sfArchitectExists = await Services.SalesforceHelper.fetchArchitect({
						email: body.email,
						mobile: body.mobile,
						customerid: body.architectCRMID,
					});

					let customerId;

					// If customer doesn't exist in Salesforce and is not an architect, create in Salesforce
					if (sfCustomerExists.customCode !== 200 && !body.isArchitect) {
						const sfRes = await Services.SalesforceHelper.registerCustomer({
							name: body.name,
							email: body.email,
							mobile: body.mobile,
							customerSource: body.customerSource,
							salesRepPID: body.salesRepPID,
							referredByArchID: body.referredByArchID,
							addressList: body.addressList,
						});
						logger.debug(sfRes);
						if (sfRes.customCode !== 200) {
							return res.json({ ok: false, message: sfRes.message });
						}
						body.customerCRMID = sfRes.customerCRMID;
						body.addressList = body.addressList.map((address, i) => ({
							...address,
							addressCRMID: sfRes.addressList[i].addressCRMID,
						}));
					} else if (sfArchitectExists.customCode !== 200 && body.isArchitect) {
						// If customer doesn't exist in Salesforce and is an architect, create architect in Salesforce
						const sfRes = await Services.SalesforceHelper.registerArchitect({
							name: body.name,
							email: body.email || "",
							mobile: body.mobile,
							firmName: body.firmName,
							salesRepPID: body.salesRepPID,
							addressList: body.addressList,
						});
						logger.debug(sfRes);
						if (sfRes.customCode !== 200) {
							return res.json({ ok: false, message: sfRes.message });
						}
						body.architectCRMID = sfRes.ArchitectCRMID;
						body.addressList = body.addressList.map((address, i) => ({
							...address,
							addressCRMID: sfRes.addressList[i].addressCRMID,
						}));
					}

					// If customer doesn't exist locally, create in local database
					if (!localCustomerExists.ok) {
						const createResult = await Services.Customers.create(body);
						if (!createResult.ok) {
							return res.json({ ok: false, message: "Failed to create customer locally" });
						}
						customerId = createResult.re1.insertedId.toString();

						// Create address if provided
						if (body.addressList && body.addressList.length > 0) {
							await Services.Address.createAddress({
								addressList: body.addressList,
								customerId: customerId.toString(),
							});
						}
						// after creating the customer in local db, create a contact in Zoho
						if (!body.isArchitect) {
							Services.ZohoHelper.createContactDuringRegistration({
								...body,
								addressFromPayload: body?.addressList[0],
							});
						}
					} else {
						customerId = localCustomerExists.data[0]._id;
					}

					// Start session for the customer
					await Services.Customers.session({ customer: customerId, isActive: true });

					// Return success response
					res.json({
						ok: true,
						message: `${
							body.isArchitect ? "Architect" : "Customer"
						} processed successfully and session started`,
						data: { _id: customerId },
					});
				} catch (e) {
					// Handle any errors
					res.json({ ok: false, message: e.message });
					logger.error(e);
				}
			},
		},

		/**
		 * Get Customer
		 * This route is used to getting customer
		 */
		"GET /": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async function (req, res) {
				try {
					const query = req.query;
					if (query?.isArchitect === "true") {
						query.isArchitect = true;
					}
					if (query?.isArchitect === "false") {
						query.isArchitect = false;
					}
					const data = await Services.Customers.getCustomer(query);
					res.json(data);
				} catch (e) {
					res.status(500).json({ ok: false, message: e.message });
					logger.error(e);
				}
			},
		},

		/**
		 * Update Customer & address
		 * This route is used for updating the normal & anonymous customer details
		 */
		"PUT /": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async function (req, res) {
				try {
					const db = await Services.MongoHelper.getDatabaseConnection();
					const { body } = req;
					let updateResult, sfResponse;

					// Anonymous customer update branch (using code)
					if (body.code) {
						// Fetch anonymous customer data using the code
						const customerData = await Services.Customers.getCustomer({ code: body.code });
						if (!customerData.ok) {
							return res.json({ ok: false, message: `Customer with code ${body.code} not found` });
						}

						if (body?.generateMobile) {
							// generate a new mobile number if not provided in the request
							const newMobileNumber = await generateNonExistingMobileNumber();
							body.mobile = newMobileNumber;
						}
						// Check if another customer already exists with the given mobile number
						const existingCustomers = await Services.Customers.getCustomersByQuery([
							{
								$match: {
									mobile: { $regex: body.mobile.replace(/^\+/, "") },
									code: { $ne: body.code }, // exclude the anonymous customer itself
								},
							},
						]);

						if (existingCustomers.length > 0) {
							// Found another customer with the same mobile.
							// Get the anonymous customer's cart.
							const anonymousCustomerCRMID = customerData.data[0].customerCRMID;
							const anonymousCart = await db
								.collection("carts")
								.findOne({ customerCRMID: anonymousCustomerCRMID });
							// Get the regular customerAddress
							const regularCustomerAddress = await db
								.collection("addresses")
								.findOne({ primaryAddress: true, customerId: existingCustomers[0]._id.toString() });
							if (anonymousCart) {
								// Assume the first matching existing customer is the one to update.
								const existingCustomer = existingCustomers[0];
								const existingCustomerCRMID = existingCustomer.customerCRMID;

								// Update the existing customer's cart with the anonymous customer's cart items.
								await Services.Carts.addProductToCart({
									customerCRMID: existingCustomerCRMID,
									addressCRMID: regularCustomerAddress.addressCRMID, // use appropriate addressCRMID for regular customer cart
									architectCRMID: body.referredByArchID || "",
									itemList: anonymousCart.itemList,
									salesRepPID: body.salesRepPID,
								});

								// Empty the anonymous customer's cart.
								await db
									.collection("carts")
									.updateOne({ cartCRMID: anonymousCart.cartCRMID }, { $set: { itemList: [] } });
							}

							// Return the same success response as a new registration.
							return res.json({
								ok: true,
								message: "Customer details updated successfully",
								data: { insertedId: existingCustomers[0]._id },
							});
						}

						// If no conflict exists, proceed with Salesforce registration for the anonymous customer.
						const sfCustomerData = {
							name: body.name || "",
							email: body.email || "",
							mobile: body.mobile,
							customerSource: body.customerSource || "",
							salesRepPID: body.salesRepPID,
							referredByArchID: body.referredByArchID || "",
							addressList:
								body.addressList.length === 0
									? [{ street: "", city: "", pincode: "" }]
									: body.addressList,
						};

						try {
							// Register customer in Salesforce
							sfResponse = await Services.SalesforceHelper.registerCustomer(sfCustomerData);
							if (sfResponse.customCode === 200) {
								// Update (or create) customer in our database
								updateResult = await Services.Customers.create({
									customerCRMID: sfResponse.customerCRMID,
									name: sfCustomerData.name,
									email: sfCustomerData.email,
									mobile: sfCustomerData.mobile,
									...(body.customerSource && { customerSource: body.customerSource }),
									...(body.salesRepPID && { salesRepPID: body.salesRepPID }),
									...(body.referredByArchID && { referredByArchID: body.referredByArchID }),
								});

								// Create address if provided
								if (sfCustomerData.addressList && sfCustomerData.addressList.length > 0) {
									await Services.Address.createAddress({
										addressList: sfCustomerData.addressList.map((address, i) => ({
											...address,
											addressCRMID: sfResponse.addressList[i].addressCRMID,
										})),
										customerId: updateResult.re1.insertedId.toString(),
									});
								}

								// Create contact in Zoho
								await Services.ZohoHelper.createContactDuringRegistration({
									email: body.email,
									mobile: body.mobile,
									name: body.name,
									addressFromPayload: body?.addressList[0],
								});

								// Get the cart for the anonymous customer code
								const cart = await db.collection("carts").findOne({
									customerCRMID: customerData.data[0].customerCRMID,
								});
								logger.debug(cart);

								// After creating the customer in Zoho, create/update cart for the customer.
								await Services.Carts.addProductToCart({
									customerCRMID: sfResponse.customerCRMID,
									addressCRMID: sfResponse.addressList[0].addressCRMID,
									architectCRMID: body.referredByArchID || "",
									itemList: cart ? cart.itemList : [],
									salesRepPID: body.salesRepPID,
								});
								// Empty the cart for the anonymous customer
								if (cart) {
									await db
										.collection("carts")
										.updateOne({ cartCRMID: cart.cartCRMID }, { $set: { itemList: [] } });
								}
							} else {
								return res.json({ ok: false, message: sfResponse.message });
							}
						} catch (sfError) {
							logger.error("Error creating customer in Salesforce", sfError);
							return res
								.status(500)
								.json({ ok: false, message: "Error creating customer in Salesforce" });
						}
					} else if (body._id) {
						try {
							// Fetch existing customer data
							const customerData = await Services.Customers.getCustomer({
								_id: body._id,
								...(body?.isArchitect ? { isArchitect: body.isArchitect } : { isArchitect: false }),
							});
							if (!customerData.ok || customerData.data.length === 0) {
								return res.json({ ok: false, message: "Customer not found" });
							}

							if (body?.mobile || body?.email) {
								// Check if the email or mobile received in payload belongs to other customers in db.
								const [existingPhone, existingEmail] = await Promise.all([
									body.mobile
										? Services.Customers.getCustomersByQuery([
												{
													$match: {
														_id: { $ne: ObjectId.createFromHexString(body._id) },
														mobile: body.mobile,
														...(body?.isArchitect
															? { isArchitect: body.isArchitect }
															: { isArchitect: false }),
													},
												},
										  ])
										: [],
									body.email
										? Services.Customers.getCustomersByQuery([
												{
													$match: {
														_id: { $ne: ObjectId.createFromHexString(body._id) },
														email: body.email,
														...(body?.isArchitect
															? { isArchitect: body.isArchitect }
															: { isArchitect: false }),
													},
												},
										  ])
										: [],
								]);

								if (existingEmail.length > 0) {
									return res.json({
										ok: false,
										message: "This Email is already registered to another Customer",
									});
								}
								if (existingPhone.length > 0) {
									return res.json({
										ok: false,
										message: "This Phone number is already registered to another Customer",
									});
								}
							}

							const customer = customerData.data[0];
							// update customer and its address in zoho
							if (customer.customer_id) {
								// find the primary address from addresslist
								const primaryAddress = body.addressList.find((a) => a.primaryAddress === true);
								const zohoResponse = await Services.ZohoHelper.updateContact({
									customer,
									address: primaryAddress,
								});

								logger.debug("zohoResponse", zohoResponse);
								// update contact_id in our database
								await db
									.collection("customers")
									.findOneAndUpdate(
										{ _id: ObjectId.createFromHexString(body._id) },
										{ $set: { customer_id: zohoResponse.contact.contact_id } }
									);
							} else {
								zohoResponse = await Services.ZohoHelper.createContactDuringRegistration({
									email: body.email,
									mobile: body.mobile,
									name: body.name,
									addressFromPayload: body?.addressList[0],
								});
							}

							// Update customer and address in Salesforce
							sfResponse = await Services.SalesforceHelper.updateAddressAndCustomer({
								customerCRMID: customer.customerCRMID,
								name: body.name,
								email: body.email,
								mobile: body.mobile,
								addressList: body.addressList || [],
								referredByArchID: body.referredByArchID,
							});
							logger.debug(sfResponse);

							if (sfResponse.customCode === 200) {
								// Update customer in our database
								updateResult = await Services.Customers.updateCustomer({
									_id: body._id,
									name: body.name,
									email: body.email,
									mobile: body.mobile,
									referredByArchID: body.referredByArchID,
									otherDetails: body.otherDetails,
									whatsappConcent: body.whatsappConcent,
									...(body.visitAgain !== undefined ? { visitAgain: body.visitAgain } : {}),
								});

								// Update address if provided
								if (body.addressList && body.addressList.length > 0) {
									await Services.Address.updateAddress({
										addressId: body.addressList[0].addressId,
										addressList: body.addressList,
									});
								}
							} else {
								return res.json({ ok: false, message: sfResponse.message });
							}
						} catch (sfError) {
							logger.error("Error updating customer in Salesforce", sfError);
							return res.status(500).json({ ok: false, message: sfError.message });
						}
					} else {
						return res.json({ ok: false, message: "Invalid request: missing code or _id" });
					}

					// Send response based on update result
					if (updateResult && updateResult.ok) {
						return res.json({
							ok: true,
							message: "Customer details updated successfully",
							data: updateResult.re1,
						});
					} else {
						return res.json({ ok: false, message: "Failed to update customer details" });
					}
				} catch (e) {
					logger.error(e);
					return res.status(500).json({ ok: false, message: e.message });
				}
			},
		},
		/**
		 * Update Architect
		 * This route is used to update architect details in the database and Salesforce
		 * @route PUT /architect
		 * @version v1.0
		 */
		"PUT /architect": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async function (req, res) {
				try {
					const { body } = req;

					// Validate architect ID
					if (!body._id || !ObjectId.isValid(body._id)) {
						return res.status(400).json({ ok: false, message: "Invalid architect ID" });
					}

					// Validate required fields
					const requiredFields = ["name", "mobile"];
					for (const field of requiredFields) {
						if (!body[field]) {
							return res.status(400).json({ ok: false, message: `${field} is required` });
						}
					}

					// Update architect in our database
					const updateResult = await Services.Customers.updateCustomer({
						_id: body._id,
						...(body.name && { name: body.name }),
						...(body.email && { email: body.email }),
						...(body.mobile && { mobile: body.mobile }),
						...(body.firmName && { firmName: body.firmName }),
						...(body.architectCRMID && { architectCRMID: body.architectCRMID }),
						...(body.visitAgain !== undefined && { visitAgain: body.visitAgain }),
						...(body.otherDetails && { otherDetails: body.otherDetails }),
						...(body.whatsappConcent !== undefined && { whatsappConcent: body.whatsappConcent }),
					});

					// Update architect's address
					const updateAddress = await Services.Address.updateAddress({
						addressId: body.addressList[0].addressId,
						addressList: body.addressList,
					});

					// If both database updates are successful, update Salesforce
					if (updateResult.ok && updateAddress.ok) {
						const sfResponse = await Services.SalesforceHelper.updateArchitect({ ...body });
						logger.debug("Salesforce update response:", sfResponse);

						return res.json({ ok: true, message: "Architect details updated successfully" });
					} else {
						return res.status(500).json({ ok: false, message: "Failed to update architect details" });
					}
				} catch (error) {
					logger.error("Error updating architect:", error);
					return res.status(500).json({ ok: false, message: error.message });
				}
			},
		},

		/**
		 * Create Session
		 * This route is used to start and end session for a user
		 */
		"POST /session": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async function (req, res) {
				try {
					const { body } = req;
					if (!body.customer || typeof body.isActive !== "boolean") {
						return res.json({
							ok: false,
							message: "isActive and customer are required for managing a session",
						});
					}
					const data = await Services.Customers.session(body);
					if (data.ok) {
						return res.json({ ok: true, message: "Session updated successfully" });
					}
					res.json({ ok: false, message: "Failed to update session" });
				} catch (e) {
					res.status(500).json({ ok: false, message: e.message });
					logger.error(e);
				}
			},
		},

		/**
		 * Get Active Customer
		 * This route is used to fetch customers whose session is active
		 */
		"GET /active": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async function (req, res) {
				try {
					const data = await Services.Customers.getActiveCustomers(req.query.search);
					if (data.ok) {
						return res.json({ ok: true, data: data.data });
					}
					res.json(data);
				} catch (e) {
					res.status(500).json({ ok: false, message: e.message });
					logger.error(e);
				}
			},
		},

		/**
		 * Get SalesPerson
		 * This route is used to fetch salespersons list
		 */
		"GET /sales-persons": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async function (req, res) {
				try {
					const data = await Services.Customers.getSalesPersons();
					if (data.ok) {
						return res.json({ ok: true, data: data.data });
					}
					res.json(data);
				} catch (e) {
					res.status(500).json({ ok: false, message: e.message });
					logger.error(e);
				}
			},
		},

		/**
		 * Sync Customer
		 * This route is used to sync customer information used by sf
		 */
		"POST /sync-customer": {
			version: "v1.0",
			handler: async function (req, res) {
				try {
					const { name, email, mobile, customerCRMID, addressList } = req.body;

					if (!name || !mobile || !customerCRMID || !addressList) {
						return res.status(400).json({
							ok: false,
							message: "name, mobile, customerCRMID and addressList are required fields",
						});
					}

					const result = await Services.Customers.updateEntity((type = "customer"), (data = req.body));

					if (result.ok) {
						return res.json({ ok: true, message: result.message, data: result.data });
					}

					res.status(400).json({ ok: false, message: result.message });
				} catch (e) {
					res.status(500).json({ ok: false, message: e.message });
					logger.error(e);
				}
			},
		},

		/**
		 * Sync Architect
		 * This route is used to sync architect information used by sf
		 */
		"POST /sync-architect": {
			version: "v1.0",
			handler: async function (req, res) {
				try {
					const { name, architectCRMID, bdmName, email, mobile } = req.body;

					if (!name || !architectCRMID || !bdmName || !mobile) {
						return res.status(400).json({
							ok: false,
							message: "name, architectCRMID, bdmName and mobile are required fields",
						});
					}

					const result = await Services.Customers.updateEntity((type = "architect"), (data = req.body));

					if (result.ok) {
						return res.json({ ok: true, message: result.message, data: result.data });
					}

					res.status(400).json({ ok: false, message: result.message });
				} catch (e) {
					res.status(500).json({ ok: false, message: e.message });
					logger.error(e);
				}
			},
		},

		/**
		 * Customer Feedback
		 * This route is used to submit customer feedback for the current session
		 */
		"POST /feedback": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async function (req, res) {
				try {
					const { body } = req;
					const data = await Services.Customers.submitFeedback(body);
					if (data.ok) {
						return res.json({ ok: true, message: "Feedback submitted successfully" });
					}
					res.json(data);
				} catch (e) {
					res.status(500).json({ ok: false, message: e.message });
					logger.error(e);
				}
			},
		},

		/**
		 * Get Leads
		 * This route is used to get leads for the current SalesRep
		 */
		"GET /leads": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async function (req, res) {
				try {
					const { employeecode, pageNo = 1, pageSize = 20, sort = -1, createdAt, search } = req.query;
					if (!employeecode) {
						return res.status(400).json({ ok: false, message: "employeecode is required" });
					}

					const options = {
						page: parseInt(pageNo, 10),
						limit: parseInt(pageSize, 10),
						sort: { createdAt: parseInt(sort, 10) },
					};

					const filter = { createdBy: employeecode };
					if (search) {
						filter.$or = [
							{ name: { $regex: search, $options: "i" } },
							{ mobile: { $regex: search, $options: "i" } },
						];
					}
					if (createdAt) {
						const date = new Date(createdAt);
						const nextDay = new Date(date);
						nextDay.setDate(date.getDate() + 1);
						filter.createdAt = { $gte: date, $lt: nextDay };
					}

					const data = await Services.Customers.getLeads(filter, options);
					if (data.ok) {
						return res.json({ ok: true, data: data.data });
					}
					res.json(data);
				} catch (e) {
					res.status(500).json({ ok: false, message: e.message });
					logger.error(e);
				}
			},
		},

		/**
		 * Add Note
		 * This route is used to add a note for a customer or salesRep
		 */
		"POST /notes": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async function (req, res) {
				try {
					const { body } = req;
					if (!body.employeecode) {
						return res
							.status(400)
							.json({ ok: false, message: "employeecode is required customerCRMID is Optional " });
					}
					const data = await Services.Customers.addNote(body);
					if (data.ok) {
						return res.json({ ok: true, message: "Note added successfully", data: data.data });
					}
					res.json(data);
				} catch (e) {
					res.status(500).json({ ok: false, message: e.message });
					logger.error(e);
				}
			},
		},

		/**
		 * Get Notes
		 * This route is used to fetch notes for a customer or salesRep
		 */
		"GET /notes": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async function (req, res) {
				try {
					const { customerCRMID, employeecode } = req.query;
					if (!customerCRMID && !employeecode) {
						return res
							.status(400)
							.json({ ok: false, message: "customerCRMID or employeecode is required" });
					}
					const data = await Services.Customers.getNotes({ customerCRMID, employeecode });
					if (data.ok) {
						return res.json({ ok: true, data: data.data });
					}
					res.json(data);
				} catch (e) {
					res.status(500).json({ ok: false, message: e.message });
					logger.error(e);
				}
			},
		},

		/**
		 * Update Note
		 * This route is used to update a note for a customer or salesRep
		 */
		"PUT /notes": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async function (req, res) {
				try {
					const { body } = req;
					if (!body.noteId) {
						return res.status(400).json({ ok: false, message: "noteId is required" });
					}
					const data = await Services.Customers.updateNote(body);
					if (data.ok) {
						return res.json({ ok: true, message: "Note updated successfully", data: data.data });
					}
					res.json(data);
				} catch (e) {
					res.status(500).json({ ok: false, message: e.message });
					logger.error(e);
				}
			},
		},

		/**
		 * Delete Note
		 * This route is used to delete a note for a customer or salesRep
		 */
		"DELETE /notes": {
			version: "v1.0",
			localMiddlewares: ["isLoggedIn"],
			handler: async function (req, res) {
				try {
					const { noteId } = req.query;
					if (!noteId) {
						return res.status(400).json({ ok: false, message: "noteId is required" });
					}
					const data = await Services.Customers.deleteNote(noteId);
					if (data.ok) {
						return res.json({ ok: true, message: "Note deleted successfully" });
					}
					res.json(data);
				} catch (e) {
					res.status(500).json({ ok: false, message: e.message });
					logger.error(e);
				}
			},
		},
	};
};
