/**
 * Products Service
 * Products service functions defined here
 */
const { ObjectId } = require("mongodb");
const { logger } = require("../utils");
module.exports = async function ({ Services, config }) {
	return {
		// Function to get products
		getProducts: async ({
			pageNo = 1,
			pageSize = 20,
			search,
			category,
			isDeleted = false,
			subCategories,
			brands,
			stock,
			color,
			price,
			brand, // this brand is for sorting in ascending or descending order
			name, // this name is for sorting in ascending or descending order
			inventory,
			productType = "MTS",
		}) => {
			try {
				const db = await Services.MongoHelper.getDatabaseConnection();
				pageSize = Number(pageSize);
				const skip = (pageNo - 1) * pageSize;

				// Build query filters
				const queryFilters = [];

				if (subCategories && subCategories.length > 0) {
					queryFilters.push({ subCategory: { $in: subCategories } });
				}
				if (brands && brands.length > 0) {
					queryFilters.push({ Brand: { $in: brands } });
				}
				if (stock !== undefined) {
					queryFilters.push(stock ? { stock: { $gt: 0 } } : { stock: { $lte: 0 } });
				}

				// Ensure isDeleted is added as a proper object
				queryFilters.push({ isDeleted: isDeleted });

				// Build category filters
				const categoryFilters = [];
				if (category && category.trim() !== "") {
					categoryFilters.push({ category }); // Only add if category is not empty
				}

				// if product type is MTS, then don't show services
				if (productType === "MTS") {
					categoryFilters.push({ category: { $ne: "SERVICES" } });
				}

				// Ensure queryFilters and categoryFilters are valid before adding to $and
				const query = {};
				if (categoryFilters.length > 0 || queryFilters.length > 0) {
					query.$and = [...categoryFilters, ...queryFilters];
				}

				// Add search condition correctly
				if (search && search.trim() !== "") {
					const searchRegex = new RegExp(search, "i");
					query.$and = query.$and || []; // Ensure $and exists before pushing
					query.$and.push({
						$or: [
							{ name: searchRegex },
							{ description: searchRegex },
							{ code: searchRegex },
							{ Brand: searchRegex },
							{ VendorSKUCode: searchRegex },
						],
					});
				}

				// Sort query
				const sortQuery = {};
				if (price) sortQuery.mrp = price;
				if (color) sortQuery.color = color;
				if (brand) sortQuery.Brand = brand;
				if (name) sortQuery.description = name;
				if (inventory) sortQuery.stock = inventory;
				if (Object.keys(sortQuery).length === 0) {
					sortQuery.description = 1;
				}
				logger.debug(sortQuery);
				// Fetch data and count in parallel
				const [data, totalCount] = await Promise.all([
					db.collection("products").find(query).sort(sortQuery).skip(skip).limit(pageSize).toArray(),
					db.collection("products").countDocuments(query),
				]);

				return data.length > 0 ? { ok: true, data, totalCount } : { ok: false, message: "No Products Found" };
			} catch (e) {
				logger.error(e);
				return { ok: false, message: e.message };
			}
		},

		// Function to create a hold request
		createHoldRequest: async ({
			skuCode,
			warehouseWiseQuantity,
			duration,
			category,
			subCategory,
			customerCrmId,
			addressCrmId,
			salesRepPid,
			architectCrmId = "",
			productId,
			customerId,
			comments = "",
		}) => {
			try {
				const db = await Services.MongoHelper.getDatabaseConnection();
				// check if quantites are valid
				const arrOfQuantity = Object.values(warehouseWiseQuantity);
				let sumOfQuantity = 0;
				arrOfQuantity.forEach((item) => {
					if (!item || item < 1) {
						return res.json({ ok: false, message: "Quantity cannot be less than 1" });
					}
					sumOfQuantity += item;
				});

				// Step 1: Check if the requested quantity is available in the stock
				const checkStock = await db
					.collection("products")
					.findOne({ _id: ObjectId.createFromHexString(productId) });
				// logger.debug(checkStock)
				if (checkStock.stock < sumOfQuantity) {
					return {
						ok: false,
						message: `The requested quantity of ${sumOfQuantity} for the product is not available.`,
					};
				}
				// Step 1.5: Create hold request in Salesforce
				const sfResponse = await Services.SalesforceHelper.holdRequest({
					skuCode,
					quantity: sumOfQuantity,
					duration,
					category,
					subCategory,
					customerCrmId,
					addressCrmId,
					salesRepPid,
					architectCrmId,
					productId,
					customerId,
					comments,
					SKUurl: `${config.FRONTEND_URL}/#/product-details/${skuCode}`,
				});

				logger.info(sfResponse);
				if (sfResponse.responses[0].customCode === 0) {
					return {
						ok: false,
						message: sfResponse.responses[0].message,
					};
				}
				// Step 2: Create a new hold request
				const data = await db.collection("holdRequests").insertOne({
					productId,
					customerId,
					skuCode,
					warehouseWiseQuantity,
					duration,
					category,
					subCategory,
					customerCrmId,
					addressCrmId,
					salesRepPid,
					architectCrmId,
					holdStatus: sfResponse.responses[0].holdstatus,
					holdCRMID: sfResponse.responses[0].holdcrmid,
					OpportunityID: sfResponse.responses[0].OpportunityID,
					comments,
					createdAt: new Date(),
					updatedAt: new Date(),
					isDeleted: false,
				});
				return { ok: true, message: "Hold request created successfully", data };
			} catch (e) {
				logger.error(e);
				return {
					ok: false,
					message: "We are facing an issue with our database, please resume after a while.",
				};
			}
		},

		// This function is used to create an unhold request for a product by a customer
		createUnholdRequest: async ({ salesRepPid, holdCRMID }) => {
			try {
				// Get the database connection
				const db = await Services.MongoHelper.getDatabaseConnection();
				// Check if a hold request exists for the product and customer
				const checkRequest = await db.collection("holdRequests").findOne({ holdCRMID });
				// If no hold request exists, return an error message
				if (checkRequest === null) {
					return { ok: false, message: "Hold request for this product by this customer doesn't exist." };
				}

				// Call unhold request for Salesforce
				const sfResponse = await Services.SalesforceHelper.unholdRequest({
					holdCRMID,
					salesRepPID: salesRepPid,
				});

				// Check if Salesforce unhold request was successful
				if (!sfResponse || sfResponse.customCode !== 1) {
					logger.debug("Failed to create unhold request in Salesforce");
					return { ok: false, message: sfResponse.message };
				}

				const unholdRes = await Services.Products.updateHoldRequest({
					holdReqCRMID: holdCRMID,
					type: "unhold",
					status: sfResponse.message,
				});
				return unholdRes;
			} catch (e) {
				// Log any errors
				logger.error(e);
				// Return an error message
				return {
					ok: false,
					message: "We are facing an issue with our database, please resume after a while.",
				};
			}
		},

		getHoldRequest: async ({
			pageNo = 1,
			pageSize = 200,
			productId,
			customerCRMID,
			search,
			me,
			employeecode,
			onHold,
		}) => {
			try {
				// Get database connection
				const db = await Services.MongoHelper.getDatabaseConnection();

				// Convert pageSize to number and calculate skip value for pagination
				pageSize = Number(pageSize);
				const skip = (pageNo - 1) * pageSize;

				// Construct the query object based on provided parameters
				const query = {
					...(productId ? { productId } : {}),
					...(customerCRMID ? { customerCrmId: customerCRMID } : {}),
					...(onHold === "true" ? { onHold: true } : {}),
					...(onHold === "false" ? { onHold: false } : {}),
					...(me === "true" ? { salesRepPid: employeecode } : {}),
					...(me === "false" ? { salesRepPid: { $ne: employeecode } } : {}),
					isDeleted: false,
				};

				// Perform aggregation to fetch hold requests with related data
				const data = await db
					.collection("holdRequests")
					.aggregate([
						// Match documents based on the constructed query
						{ $match: query },
						// Convert string IDs to ObjectId
						{
							$addFields: {
								customerId: { $toObjectId: "$customerId" },
							},
						},
						// Lookup customer details
						{
							$lookup: {
								from: "customers",
								localField: "customerId",
								foreignField: "_id",
								as: "customerDetails",
							},
						},
						// Unwind the customerDetails array
						{ $unwind: "$customerDetails" },
						// Apply search filter on customer name if provided
						{
							$match: {
								...(search !== undefined
									? { "customerDetails.name": { $regex: search, $options: "i" } }
									: {}),
							},
						},
						// Lookup sales person details
						{
							$lookup: {
								from: "salesPersons",
								localField: "salesRepPid",
								foreignField: "employeecode",
								as: "salesDetails",
							},
						},
						// Unwind the salesDetails array
						{ $unwind: "$salesDetails" },
						// Lookup product details
						{
							$lookup: {
								from: "products",
								localField: "skuCode",
								foreignField: "code",
								as: "productDetails",
							},
						},
						// Unwind the productDetails array
						{ $unwind: "$productDetails" },
						// Sort results by _id in descending order
						{ $sort: { _id: -1 } },
						// Apply pagination
						{ $skip: skip },
						{ $limit: pageSize },
					])
					.toArray();

				// Get total count of documents matching the query
				const totalCount = await db.collection("holdRequests").countDocuments(query);

				// Return results
				if (data.length > 0) {
					return { ok: true, data, totalCount };
				}
				return { ok: false, message: "No hold requests found", totalCount: 0 };
			} catch (e) {
				// Log any errors
				logger.error(e);
				// Return error message
				return {
					ok: false,
					message: "We are facing an issue with our database, please resume after a while.",
				};
			}
		},
		getInventoryStatus: async ({ sku, quantity }) => {
			try {
				const db = await Services.MongoHelper.getDatabaseConnection();
				const data = await db.collection("products").findOne({ code: sku }, { projection: { stock: 1 } });
				if (data === null) {
					return { ok: false, message: "Invalid SKU" };
				}
				if (data?.stock) {
					return { ok: true, data };
				}
				return { ok: false, message: "Inventory status is not ok" };
			} catch (e) {
				logger.error(e);
				return {
					ok: false,
					message: "We are facing an issue with our database, please resume after a while.",
				};
			}
		},
		updateHoldRequest: async ({ holdReqCRMID, status, comments, categoryHeadName, type }) => {
			try {
				const db = await Services.MongoHelper.getDatabaseConnection();

				// Fetch the hold request document
				const holdReq = await db.collection("holdRequests").findOne({ holdCRMID: holdReqCRMID });
				if (!holdReq) {
					return { ok: false, message: "Hold request not found" };
				}

				// Prevent duplicate approvals/rejections
				if (type === "hold" && holdReq.holdStatus === "Hold Approved") {
					return { ok: false, message: "This Hold Request is already Approved." };
				}
				if (type === "unhold" && holdReq.holdStatus === "Status updated to Unhold Successfully") {
					return { ok: false, message: "This unHold Request is already Accepted" };
				}

				// Build the update fields for the hold request
				const updateFields = {
					...(status !== undefined ? { holdStatus: status } : {}),
					...(comments !== undefined ? { comments } : { comments: "" }),
					...(categoryHeadName !== undefined ? { categoryHeadName } : { categoryHeadName: "" }),
					...(type === "hold" ? {} : { isDeleted: true }),
					updatedAt: new Date(),
				};

				// Update the hold request and retrieve the updated document
				const updatedHoldRequest = await db
					.collection("holdRequests")
					.findOneAndUpdate({ holdCRMID: holdReqCRMID }, { $set: updateFields }, { returnDocument: "after" });

				if (!updatedHoldRequest) {
					return { ok: false, message: "Hold request not found after update" };
				}

				// Ensure the hold request has an associated productId
				if (!updatedHoldRequest.productId) {
					return { ok: false, message: "No productId associated with hold request" };
				}
				// const productId = ObjectId.createFromHexString(updatedHoldRequest.productId);

				// Extract warehouse quantities (defaulting to an empty object if not present)
				const warehouseQuantities = updatedHoldRequest.warehouseWiseQuantity || {};
				const quantitySum = Object.values(warehouseQuantities).reduce((sum, qty) => sum + qty, 0);
				const warehouseKeys = Object.keys(warehouseQuantities);

				// Ensure the product has 'unitsOnHold' and 'warehouseWiseQuantity' fields initialized.
				// This step makes sure the fields are the correct type before applying $inc.
				await db
					.collection("products")
					.updateOne(
						{ code: updatedHoldRequest.skuCode, isDeleted: false, unitsOnHold: { $exists: false } },
						{ $set: { unitsOnHold: 0 } }
					);
				await db.collection("products").updateOne(
					{
						code: updatedHoldRequest.skuCode,
						isDeleted: false,
						warehouseWiseQuantity: { $exists: false },
					},
					{ $set: { warehouseWiseQuantity: {} } }
				);

				// Build the update operation for the product based on the type (hold/unhold)
				let updateOperation = {};
				if (type === "hold") {
					updateOperation = {
						$inc: {
							unitsOnHold: quantitySum,
							...warehouseKeys.reduce((acc, key) => {
								acc[`warehouseWiseQuantity.${key}`] = warehouseQuantities[key];
								return acc;
							}, {}),
						},
					};
				} else if (type === "unhold") {
					// If updatedHoldRequest.quantity is not provided, fall back to quantitySum.
					const decQuantity = updatedHoldRequest.quantity || quantitySum;
					updateOperation = {
						$inc: {
							unitsOnHold: -decQuantity,
							...warehouseKeys.reduce((acc, key) => {
								acc[`warehouseWiseQuantity.${key}`] = -warehouseQuantities[key];
								return acc;
							}, {}),
						},
					};
				}

				// Log the update operation for debugging purposes
				logger.debug("Product update operation:", updateOperation);

				const productUpdateResult = await db
					.collection("products")
					.updateOne({ code: updatedHoldRequest.skuCode, isDeleted: false }, updateOperation);
				logger.debug(`Product update result for ${type}:`, productUpdateResult);

				// For debugging: Fetch the updated product document
				// const updatedProduct = await db.collection("products").findOne({ code: updatedHoldRequest.skuCode, isDeleted: false });
				// logger.debug("Updated product document after update:", updatedProduct);

				return { ok: true, message: "Hold request updated successfully" };
			} catch (e) {
				logger.error(e);
				return {
					ok: false,
					message: "We are facing an issue with our database, please try again later.",
				};
			}
		},

		getCategoryAndBrandsList: async () => {
			const db = await Services.MongoHelper.getDatabaseConnection();
			const data = await db
				.collection("products")
				.aggregate([
					{
						$match: {
							isDeleted: false,
						},
					},
					{
						$group: {
							_id: "$category",
							subcategories: { $addToSet: "$subCategory" },
							brands: { $addToSet: "$Brand" },
						},
					},
					{
						$project: {
							_id: 0,
							category: "$_id",
							subcategories: "$subcategories",
							brands: "$brands",
						},
					},
					{
						$sort: { category: 1 },
					},
				])
				.toArray();
			return { ok: true, data };
		},

		createProducts: async (data) => {
			try {
				const db = await Services.MongoHelper.getDatabaseConnection();

				// Ensure `data` is in an array format
				data = [data.item];

				// Validate that `data` is a non-empty array
				if (!Array.isArray(data) || data.length === 0) {
					return { ok: false, message: "Invalid input: data must be a non-empty array" };
				}

				// Check if required fields (`item_id` and `sku`) are present in each product
				const invalidProducts = data.filter((item) => !item.item_id || !item.sku);
				if (invalidProducts.length > 0) {
					return { ok: false, message: "item_id and sku are required for all products" };
				}

				// Prepare bulk operations for inserting or updating products
				const bulkOps = data.map((item) => ({
					updateOne: {
						// Filter condition: Find product by `sku`
						filter: { code: item.sku },
						update: {
							// Fields that will be updated or inserted
							$set: {
								code: item.sku,
								createdByZoho: true,
								updatedAt: new Date(), // Update timestamp on every update
								stock: item.available_for_sale_stock || 0,
								price: +item.purchase_rate,
								mrp: +item.label_rate,
								discount: +item.custom_field_hash?.cf_structured_discount || 0,
								hsn_or_sac: item?.hsn_or_sac || "",
								unit: item.unit,
								status: item.status,
								tax_id: item.tax_id || "",
								tax_name: item.tax_name || "",
								item_type: item.item_type,
								tax_exemption_code: item.tax_exemption_code,
								warehouse_name: item?.warehouses[0]?.warehouse_name || "",
								project_id: item?.project_id || "",
								template_id: item?.associated_template_id || "",
								documents: item?.documents || [],
								warehouses:
									item?.warehouses?.map((wareH) => ({
										warehouse_id: wareH.warehouse_id,
										warehouse_name: wareH.warehouse_name,
										warehouse_stock: wareH.warehouse_available_for_sale_stock,
									})) || [],
								item_tax_preferences: item.item_tax_preferences,
							},
							// Fields that will only be set when inserting a new document
							$setOnInsert: {
								createdAt: new Date(), // Only set on insert
								item_id: item.item_id, // Only set on insert to avoid conflicts
								isDeleted: false,
							},
						},
						upsert: true,
					},
				}));

				// Execute bulk write operation on the `products` collection
				const result = await db.collection("products").bulkWrite(bulkOps);

				// Log the operation results
				logger.info(
					`Products operation completed. Upserted: ${result.upsertedCount}, Modified: ${result.modifiedCount}`
				);
				// Return success response
				return {
					ok: true,
					upsertedCount: result.upsertedCount,
					modifiedCount: result.modifiedCount,
					message: `${result.upsertedCount} products created, ${result.modifiedCount} products updated`,
				};
			} catch (error) {
				logger.error("Error in createProducts:", error);
				return { ok: false, message: error.message };
			}
		},

		getProductsByQuery: async ({ query }) => {
			const db = await Services.MongoHelper.getDatabaseConnection();
			const result = await db.collection("products").aggregate(query).toArray();
			return result;
		},

		updateProductImages: async ({ code, images, employeecode }) => {
			const db = await Services.MongoHelper.getDatabaseConnection();
			const imageInfo = images.map((image) => {
				return { image, uploadedBy: employeecode, updatedAt: new Date(), uploadedFrom: "Foyr" };
			});
			const result = await db.collection("products").updateOne({ code }, [
				{
					$set: {
						productImages: {
							$concatArrays: [
								{ $ifNull: ["$productImages", []] },
								{
									$filter: {
										input: imageInfo,
										as: "newImage",
										cond: {
											$not: {
												$in: [
													"$$newImage.image",
													{
														$map: {
															input: { $ifNull: ["$productImages", []] },
															as: "existing",
															in: "$$existing.image",
														},
													},
												],
											},
										},
									},
								},
							],
						},
					},
				},
			]);
			if (result.modifiedCount > 0) {
				Services.CronJobs.convertToBase64Bulk({ skus: [code] });
				return { ok: true, message: "Product images updated successfully" };
			} else if (result.matchedCount === 0) {
				return { ok: false, message: "No product found with the given sku" };
			} else {
				return { ok: false, message: "Failed to update product images" };
			}
		},

		deleteProductImage: async ({ code, image }) => {
			const db = await Services.MongoHelper.getDatabaseConnection();
			const result = await db.collection("products").updateOne(
				{ code },
				{
					$pull: {
						productImages: { image }, // Remove any object where the image field equals the provided URL
					},
				}
			);
			logger.debug(result);
			return result.modifiedCount > 0
				? { ok: true, message: "Product image removed successfully" }
				: { ok: false, message: "Failed to remove product image" };
		},

		updateProductImage: async function ({ code, oldImage, newImage, employeecode }) {
			const db = await Services.MongoHelper.getDatabaseConnection();
			const result = await db.collection("products").updateOne(
				{ code, "productImages.image": oldImage },
				{
					$set: {
						"productImages.$.image": newImage,
						"productImages.$.updatedAt": new Date(),
						"productImages.$.uploadedBy": employeecode,
						"productImages.$.uploadedFrom": "Foyr",
					},
				}
			);

			if (result.modifiedCount > 0) {
				Services.CronJobs.convertToBase64Bulk({ skus: [code] });
				return { ok: true, message: "Product images updated successfully" };
			} else if (result.matchedCount === 0) {
				return { ok: false, message: "No product found with the given sku" };
			} else {
				return { ok: false, message: "Failed to update product images" };
			}
		},

		updateProductImagesOrder: async ({ code, productImages, employeecode }) => {
			try {
				const db = await Services.MongoHelper.getDatabaseConnection();

				// Validate that productImages contains valid objects with required fields
				const isValid = productImages.every((img) => img.image && typeof img.image === "string");
				if (!isValid) {
					return { ok: false, message: "Invalid productImages array format" };
				}

				// Update the product's productImages array with the new order
				const result = await db.collection("products").updateOne(
					{ code },
					{
						$set: {
							productImages: productImages.map((img) => ({
								...img,
								updatedAt: img.updatedAt ? new Date(img.updatedAt) : new Date(),
								reorderBy: employeecode,
							})),
						},
					}
				);
				
				Services.CronJobs.convertToBase64Bulk({ skus: [code] });

				if (result.modifiedCount > 0) {
					return { ok: true, message: "Product images reordered successfully" };
				} else if (result.matchedCount === 0) {
					return { ok: false, message: "No product found with the given SKU code" };
				} else if (result.modifiedCount === 0) {
					return { ok: true, message: "Images are already in the desired order" };
				}
			} catch (e) {
				logger.error(e);
				return { ok: false, message: "An error occurred while reordering product images" };
			}
		},
	};
};
