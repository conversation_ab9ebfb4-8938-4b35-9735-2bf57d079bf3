const { logger } = require("../utils");
const { ObjectId } = require("mongodb");
const ExcelJS = require("exceljs");

module.exports = function ({ Services, config }) {
	return {
		/**
		 * Update the approval status of a quote
		 * @param {string} quoteId - The ID of the quote
		 * @param {string} status - The new approval status
		 * @param {string} quoteSalesCRMID - The Salesforce CRM ID of the quote
		 * @returns {Object} Result of the approval status update
		 */
		approvalStatus: async ({ quoteId, status, quoteSalesCRMID }) => {
			try {
				const db = await Services.MongoHelper.getDatabaseConnection();
				const quote = await db
					.collection("quotes")
					.findOneAndUpdate({ quoteCRMID: quoteId }, { $set: { QuoteStatus: status, quoteSalesCRMID } });
				if (!quote) {
					return { ok: false, message: "Quote not found" };
				}
				return { ok: true, message: "Approval Status updated successfully" };
			} catch (error) {
				logger.error(error);
				return { ok: false, message: error.message };
			}
		},

		/**
		 * Create a new quote
		 * @param {Object} quoteDetails - Details of the quote to be created
		 * @returns {Object} Result of the quote creation process
		 */
		createQuote: async ({
			customerCRMID,
			addressCRMID,
			architectCRMID = "",
			category,
			subCategory,
			itemList,
			salesRepPID,
			wishlistID_cartID,
			DISCOUNT_PERCENTAGE,
			discount_AMOUNT,
			grand_total_WITHOUT_DISCOUNT,
			grand_total_WITH_DISCOUNT,
		}) => {
			try {
				// Get database connection
				const db = await Services.MongoHelper.getDatabaseConnection();

				// Prepare quote data
				const quoteData = {
					customerCRMID,
					addressCRMID,
					architectCRMID,
					category,
					subCategory,
					itemList,
					salesRepPID,
					wishlistID_cartID: wishlistID_cartID || "",
					DISCOUNT_PERCENTAGE: DISCOUNT_PERCENTAGE || "",
					discount_AMOUNT: discount_AMOUNT || "",
					grand_total_WITHOUT_DISCOUNT: grand_total_WITHOUT_DISCOUNT || "",
					grand_total_WITH_DISCOUNT: grand_total_WITH_DISCOUNT || "",
					createdAt: new Date(),
					updatedAt: new Date(),
				};

				// Check if required fields are present
				const requiredFields = [
					"customerCRMID",
					"addressCRMID",
					"category",
					"subCategory",
					"itemList",
					"salesRepPID",
				];
				const missingFields = requiredFields.filter((field) => !quoteData[field]);
				if (missingFields.length > 0) {
					throw new Error(`Missing required fields: ${missingFields.join(", ")}`);
				}

				// Insert quote into database
				const { insertedId } = await db.collection("quotes").insertOne(quoteData);

				// Create quote in Salesforce
				const sfRes = await Services.SalesforceHelper.createQuote({
					customerCrmId: customerCRMID,
					addressCrmId: addressCRMID,
					architectCrmId: architectCRMID,
					category,
					subCategory,
					itemList,
					quoteURL: `${process.env.FRONTEND_URL}/ui/#/quote/${insertedId.toString()}`,
					salesPersonPid: salesRepPID,
					wishlistID_cartID,
					DISCOUNT_PERCENTAGE,
					discount_AMOUNT,
					grand_total_WITHOUT_DISCOUNT,
					grand_total_WITH_DISCOUNT,
				});
				logger.debug(sfRes.data);

				// Check if Salesforce quote creation was successful
				if (sfRes.data.responses[0].customCode !== 1) {
					return { ok: false, message: sfRes.data.responses[0].message };
				}

				// Extract relevant data from Salesforce response
				const { Quotestatus, QuoteCrmId, Category_level_saleCRMID } = sfRes.data.responses[0];
				logger.debug({ Quotestatus, QuoteCrmId, Category_level_saleCRMID });

				// Update quote in database with Salesforce data
				db.collection("quotes").updateOne(
					{ _id: insertedId },
					{
						$set: {
							QuoteStatus: Quotestatus,
							quoteCRMID: QuoteCrmId,
							categorySalesCRMID: Category_level_saleCRMID,
						},
					}
				);

				// Tag the architect in Salesforce
				Services.SalesforceHelper.tagArchitect({
					categorySalesCRMID: Category_level_saleCRMID,
					architectCRMID,
					salesRepPID,
				});

				// Return success response
				return {
					ok: true,
					message: "Quote created successfully",
					data: {
						quoteId: insertedId.toString(),
					},
				};
			} catch (error) {
				logger.error("Error creating quote:", error);
				return { ok: false, message: error.message };
			}
		},

		getQuote: async function ({ quoteId, quoteCRMID, customerCRMID, type, price = null, quoteType = null }) {
			try {
				const db = await Services.MongoHelper.getDatabaseConnection();
				let query = {
					// TODO: Update this line after finalizing the statuses
					// status: { $ne: "ORDER_CREATED" },
					...(quoteId ? { _id: ObjectId.createFromHexString(quoteId) } : {}),
					...(quoteCRMID ? { quoteCRMID } : {}),
					...(customerCRMID ? { customerCRMID } : {}),
					...(type ? { type } : {}),
					...(quoteType !== null ? { QUOTETYPE: quoteType } : {}),
				};

				// Validate input parameters
				if (!quoteId && !quoteCRMID && !customerCRMID) {
					return {
						ok: false,
						message: "At least one of quoteId, quoteCRMID, or customerCRMID must be provided",
					};
				}

				const quote = await db
					.collection("quotes")
					.aggregate([
						{
							$match: query,
						},
						{
							$lookup: {
								from: "products",
								localField: "itemList.productName",
								foreignField: "code",
								as: "productInfoArray",
							},
						},
						{
							$lookup: {
								from: "customers",
								localField: "customerCRMID",
								foreignField: "customerCRMID",
								as: "customerInfo",
							},
						},
						{
							$lookup: {
								from: "salesPersons",
								let: { seller_id: "$salesRepPID" },
								pipeline: [
									{
										$match: {
											$expr: { $eq: ["$employeecode", "$$seller_id"] }, // Ensure correct matching
										},
									},
									{ $limit: 1 }, // Ensure only one seller is fetched
								],
								as: "sellerInfo",
							},
						},
						{
							$lookup: {
								from: "customers", // Lookup only architects, filter within the lookup
								let: { arch_id: "$architectCRMID" },
								pipeline: [
									{
										$match: {
											$expr: {
												$and: [
													{ $eq: ["$architectCRMID", "$$arch_id"] },
													{ $eq: ["$isArchitect", true] },
												],
											}, // Ensures only anonymous architects are matched
										},
									},
									{ $limit: 1 }, // Ensure at most one result to avoid duplicates
								],
								as: "architectInfo",
							},
						},
						{
							$addFields: {
								customerInfo: { $arrayElemAt: ["$customerInfo", 0] },
								architectInfo: {
									$ifNull: [
										{ $arrayElemAt: ["$architectInfo", 0] },
										{}, // Use an empty object if architectInfo is not found
									],
								},
								sellerInfo: {
									$ifNull: [
										{ $arrayElemAt: ["$sellerInfo", 0] },
										{}, // Use an empty object if sellerInfo is not found
									],
								},
								itemList: {
									$map: {
										input: "$itemList",
										as: "item",
										in: {
											$mergeObjects: [
												"$$item",
												{
													productInfo: {
														$let: {
															vars: {
																product: {
																	$arrayElemAt: [
																		{
																			$filter: {
																				input: "$productInfoArray",
																				as: "product",
																				cond: {
																					$eq: [
																						"$$product.code",
																						"$$item.productName",
																					],
																				},
																			},
																		},
																		0,
																	],
																},
															},
															in: {
																$cond: {
																	if: { $isArray: "$$product.productImages" },
																	then: {
																		$mergeObjects: [
																			"$$product",
																			{
																				productImages: {
																					$sortArray: {
																						input: "$$product.productImages",
																						sortBy: { updatedAt: -1 },
																					},
																				},
																			},
																		],
																	},
																	else: "$$product",
																},
															},
														},
													},
												},
											],
										},
									},
								},
							},
						},
						{
							$project: {
								productInfoArray: 0,
							},
						},
						{
							$sort: {
								createdAt: -1,
								...(price !== null ? { grand_total_WITHOUT_DISCOUNT: price } : {}),
							},
						},
					])
					.toArray();

				if (quote.length === 0) {
					return { ok: false, message: "Quote not found" };
				}

				if (!quote) {
					return { ok: false, message: "Quote not found" };
				}

				return {
					ok: true,
					message: "Quote retrieved successfully",
					data: quote,
				};
			} catch (error) {
				logger.error("Error retrieving quote:", error);
				return { ok: false, message: "An error occurred while retrieving the quote" };
			}
		},

		getQuotesBatch: async function ({ quoteCRMIDs }) {
			try {
				const db = await Services.MongoHelper.getDatabaseConnection();
				if (!quoteCRMIDs || quoteCRMIDs.length === 0) {
					return { ok: false, message: "No quote IDs provided", data: [] };
				}

				const quotes = await db
					.collection("quotes")
					.aggregate([
						{ $match: { quoteCRMID: { $in: quoteCRMIDs } } },
						{
							$lookup: {
								from: "products",
								localField: "itemList.productName",
								foreignField: "code",
								as: "productInfoArray",
							},
						},
						{
							$lookup: {
								from: "customers",
								localField: "customerCRMID",
								foreignField: "customerCRMID",
								as: "customerInfo",
							},
						},
						{
							$lookup: {
								from: "salesPersons",
								let: { seller_id: "$salesRepPID" },
								pipeline: [
									{ $match: { $expr: { $eq: ["$employeecode", "$$seller_id"] } } },
									{ $limit: 1 },
								],
								as: "sellerInfo",
							},
						},
						{
							$addFields: {
								customerInfo: { $arrayElemAt: ["$customerInfo", 0] },
								sellerInfo: { $ifNull: [{ $arrayElemAt: ["$sellerInfo", 0] }, {}] },
								itemList: {
									$map: {
										input: "$itemList",
										as: "item",
										in: {
											$mergeObjects: [
												"$$item",
												{
													productInfo: {
														$arrayElemAt: [
															{
																$filter: {
																	input: "$productInfoArray",
																	as: "product",
																	cond: {
																		$eq: ["$$product.code", "$$item.productName"],
																	},
																},
															},
															0,
														],
													},
												},
											],
										},
									},
								},
							},
						},
						{ $project: { productInfoArray: 0 } },
					])
					.toArray();

				return { ok: true, message: "Quotes retrieved successfully", data: quotes };
			} catch (error) {
				logger.error("Error retrieving quotes batch:", error);
				return { ok: false, message: "An error occurred while retrieving quotes" };
			}
		},

		getQuoteByCartCRMIDAndCategorySubCategory: async ({ cartCRMID, category }) => {
			try {
				const db = await Services.MongoHelper.getDatabaseConnection();
				const quote = await db
					.collection("quotes")
					.find({ wishlistID_cartID: cartCRMID, category, QUOTETYPE: "MTS" })
					.sort({ createdAt: -1 })
					.toArray();

				if (quote.length === 0) {
					return { ok: false, message: "Quote not found" };
				}

				return {
					ok: true,
					message: "Quote retrieved successfully",
					data: quote,
				};
			} catch (error) {
				logger.error("Error retrieving quote:", error);
				return { ok: false, message: "An error occurred while retrieving the quote" };
			}
		},

		getQuoteByCustomerCRMIDAndCategorySubCategory: async ({ customerCRMID, category }) => {
			try {
				const db = await Services.MongoHelper.getDatabaseConnection();
				const quote = await db
					.collection("quotes")
					.find({ customerCRMID, category, QUOTETYPE: "MTO" })
					.sort({ createdAt: -1 })
					.toArray();

				if (quote.length === 0) {
					return { ok: false, message: "Quote not found" };
				}

				return {
					ok: true,
					message: "Quote retrieved successfully",
					data: quote,
				};
			} catch (error) {
				logger.error("Error retrieving quote:", error);
				return { ok: false, message: "An error occurred while retrieving the quote" };
			}
		},
		updateQuoteByQuery: async ({ query, update }) => {
			try {
				const db = await Services.MongoHelper.getDatabaseConnection();
				const result = await db
					.collection("quotes")
					.updateOne(query, { $set: { ...update, updatedAt: new Date() } });

				if (result.matchedCount === 0) {
					return { ok: false, message: "Quote not found" };
				}

				if (result.modifiedCount === 0) {
					return { ok: false, message: "Quote was not modified" };
				}

				return {
					ok: true,
					message: "Quote updated successfully",
					data: result,
				};
			} catch (error) {
				logger.error("Error updating quote:", error);
				return { ok: false, message: "An error occurred while updating the quote" };
			}
		},

		/**
		 * Create a new quote with versioning
		 * @param {Object} quoteDetails - Details of the quote to be created
		 * @returns {Object} Result of the quote creation process
		 */
		createQuotev2: async ({
			customerCRMID,
			addressCRMID,
			architectCRMID = "",
			category,
			quoteVersion,
			itemList,
			salesRepPID,
			wishlistID_cartID,
			DISCOUNT_PERCENTAGE,
			discount_AMOUNT,
			grand_total_WITHOUT_DISCOUNT,
			grand_total_WITH_DISCOUNT,
			comments,
			QUOTETYPE,
			quoteURL,
			quotationNo,
			payment_terms,
			showDiscount = false,
		}) => {
			try {
				// Get database connection
				const db = await Services.MongoHelper.getDatabaseConnection();

				// Prepare quote data
				const quoteData = {
					customerCRMID,
					addressCRMID,
					architectCRMID,
					category,
					itemList,
					salesRepPID,
					quoteVersion,
					wishlistID_cartID: wishlistID_cartID || "",
					DISCOUNT_PERCENTAGE: DISCOUNT_PERCENTAGE || "",
					discount_AMOUNT: discount_AMOUNT || "",
					grand_total_WITHOUT_DISCOUNT: grand_total_WITHOUT_DISCOUNT || "",
					grand_total_WITH_DISCOUNT: grand_total_WITH_DISCOUNT || "",
					createdAt: new Date(),
					updatedAt: new Date(),
					isActive: true,
					comments: comments || "",
					QUOTETYPE: QUOTETYPE || "",
					quoteURL,
					...(quotationNo ? { quotationNo } : {}),
					payment_terms: payment_terms || 0,
					...(showDiscount ? { showDiscount } : {}),
				};

				// Check if required fields are present
				const requiredFields = ["customerCRMID", "addressCRMID", "category", "itemList", "salesRepPID"];
				const missingFields = requiredFields.filter((field) => !quoteData[field]);
				if (missingFields.length > 0) {
					throw new Error(`Missing required fields: ${missingFields.join(", ")}`);
				}

				// Insert quote into database
				const { insertedId } = await db.collection("quotes").insertOne(quoteData);

				// remove the warehouse_id from each item
				let updatedItemList = itemList.map((item) => {
					const { warehouse_id, ...rest } = item;
					return rest;
				});
				if (QUOTETYPE === "MTO") {
					updatedItemList = updatedItemList.map((item) => {
						return {
							productName: item.productName,
							quantity: item.quantity,
							subCategory: item?.subCategory?.toUpperCase() || item?.subcategory?.toUpperCase(),
							discountpercentage: item.discountpercentage,
							Totalamount: item.totalNetValue,
						};
					});
				}
				// Create quote in Salesforce
				const sfRes = await Services.SalesforceHelper.createQuote({
					customerCrmId: customerCRMID,
					addressCrmId: addressCRMID,
					architectCrmId: architectCRMID,
					category: category.toUpperCase(),
					itemList: updatedItemList,
					quoteURL: `${config.FRONTEND_URL}/#/quote/${insertedId.toString()}`,
					salesPersonPid: salesRepPID,
					wishlistID_cartID,
					DISCOUNT_PERCENTAGE,
					discount_AMOUNT,
					grand_total_WITHOUT_DISCOUNT,
					grand_total_WITH_DISCOUNT,
					comments,
					QUOTETYPE,
				});
				logger.debug(sfRes.data);

				// Check if Salesforce quote creation was successful
				if (sfRes.data.responses[0].customCode !== 1) {
					await db.collection("quotes").deleteOne({ _id: insertedId });
					return { ok: false, message: sfRes.data.responses[0].message };
				}

				// Extract relevant data from Salesforce response
				const { Quotestatus, QuoteCrmId, Category_level_saleCRMID } = sfRes.data.responses[0];
				logger.debug({ Quotestatus, QuoteCrmId, Category_level_saleCRMID });

				// get address using addressCRMID and insert it in the quote.
				const addressInfo = await db.collection("addresses").findOne({ addressCRMID });

				// Update quote in database with Salesforce data
				db.collection("quotes").updateOne(
					{ _id: insertedId },
					{
						$set: {
							QuoteStatus: Quotestatus,
							quoteCRMID: QuoteCrmId,
							categorySalesCRMID: Category_level_saleCRMID,
							quoteURL: `${config.FRONTEND_URL}/#/quote/${insertedId.toString()}`,
							payment_terms,
							quotationNo,
							addressInfo,
						},
					}
				);

				// Tag the architect in Salesforce
				if (architectCRMID !== "") {
					Services.SalesforceHelper.tagArchitect({
						categorySalesCRMID: Category_level_saleCRMID,
						architectCRMID,
						salesRepPID,
					});
				}

				// Return success response
				return {
					ok: true,
					message: "Quote created successfully",
					data: {
						quoteId: insertedId.toString(),
					},
				};
			} catch (error) {
				logger.error("Error creating quote:", error);
				return { ok: false, message: error.message };
			}
		},

		/**
		 * Converts an Excel file to JSON format
		 * @param {Buffer} fileBuffer - The buffer containing the Excel file data
		 * @returns {Object} The parsed JSON data or an error object
		 */
		excelToJson: async (fileBuffer) => {
			try {
				// Create a new instance of ExcelJS Workbook
				const workbook = new ExcelJS.Workbook();

				// Read the Excel file from the buffer
				await workbook.xlsx.load(fileBuffer);

				// Get the first worksheet
				const worksheet = workbook.getWorksheet(1); // 1 is the index for the first sheet

				// Initialize an object to store the data
				const jsonData = { itemList: [] };
				let lineItemHeader = {};

				// Iterate over rows in the worksheet using a for loop
				for (let rowNumber = 1; rowNumber <= worksheet.rowCount; rowNumber++) {
					let row = worksheet.getRow(rowNumber);
					const rowData = {};

					// Extract Quotation No.
					if (rowNumber === 1) {
						jsonData.quotationNo = row.getCell("L").value;
					}

					// Extract Payment Terms
					if (rowNumber === 4) {
						jsonData.paymentTerms = row.getCell("F").value;
					}

					// Extract header row for line items
					if (rowNumber === 6) {
						for (let colNumber = 1; colNumber <= row.cellCount; colNumber++) {
							const headerValue = row.getCell(colNumber).value;
							lineItemHeader[colNumber] =
								typeof headerValue === "string" ? headerValue.trim() : headerValue;
						}
					}

					//   logger.debug(lineItemHeader);
					// Process line items starting from row 7
					if (rowNumber >= 7) {
						for (let colNumber = 1; colNumber <= row.cellCount; colNumber++) {
							// const rowData = {};
							const cell = row.getCell(colNumber);
							if (lineItemHeader[colNumber] === "SKU Code") {
								rowData["productName"] = cell.value?.trim();
							}
							if (lineItemHeader[colNumber] === "Quantity") {
								rowData.quantity = cell.value;
							}
							if (lineItemHeader[colNumber] === "Item Description") {
								rowData["description"] = cell.value?.trim();
							}
							if (lineItemHeader[colNumber] === "Category") {
								rowData["category"] = cell.value?.trim();
							}
							if (lineItemHeader[colNumber] === "Sub-Category") {
								rowData["subCategory"] = cell.value?.trim();
							}
							if (lineItemHeader[colNumber] === "Discount %") {
								rowData.discountpercentage = cell.value;
							}
							if (lineItemHeader[colNumber] === "Total Value") {
								// console.log(cell.value?.result);
								rowData.Totalamount = cell.value?.result;
							}
							if (lineItemHeader[colNumber] === "Pre-Discount Value/unit \n(Ex GST)") {
								//no
								rowData.preDiscountValue = cell.value?.result || cell.value;
							}
							if (lineItemHeader[colNumber] === "Discount Value/unit") {
								rowData.discountValue = cell.value?.result;
							}
							if (lineItemHeader[colNumber] === "Net Value/unit (Ex GST)") {
								rowData.netValue = cell.value?.result;
							}
							if (lineItemHeader[colNumber] === "Total Net Value\n(Ex GST)\nNet Value * Quantity") {
								//no
								rowData.totalNetValue = cell.value?.result;
							}
							if (lineItemHeader[colNumber] === "HSN") {
								rowData.hsn_or_sac = cell.value;
							}

							if (lineItemHeader[colNumber] === "WareHouse Name" && cell.value) {
								rowData.warehouse_id = cell.value;
							}
							if (lineItemHeader[colNumber] === "GST") {
								rowData.GST = cell.value?.formula;
							}
						}

						// Check if we've reached the end of line items
						if (isNaN(Number(row.getCell(1).value)) || !row.getCell(1).value) {
							for (let i = 0; i < 10; i++) {
								row = worksheet.getRow(rowNumber + i);
								// Search for "Grand Total" in column K
								const columnKValue = row.getCell("L").value;
								if (columnKValue && columnKValue.toString().trim() === "Grand Total") {
									// Add Grand Total key and value to jsonData
									jsonData.grandTotal = row.getCell("O").value?.result;
									break;
								}
							}
							break;
						} else {
							jsonData.itemList.push(rowData);
						}
					}
				}

				// get the warehouseIds for those skus
				const db = await Services.MongoHelper.getDatabaseConnection();
				// filter the skus which have warehouse name
				const filteredSkus = jsonData.itemList.filter((item) => item.warehouse_id);
				// get the warehouseIds for those skus
				const products = await db
					.collection("products")
					.find({ code: { $in: filteredSkus.map((item) => item.productName) } })
					.toArray();
				// add the warehouseIds to the jsonData
				jsonData.itemList.forEach((item) => {
					const product = products.find((product) => product.code === item.productName);
					if (product && product.warehouses) {
						const warehouse = product.warehouses.find(
							(warehouse) => warehouse.warehouse_name === item.warehouse_id
						);
						if (warehouse) {
							item.warehouse_id = warehouse.warehouse_id;
						}
					}
				});

				// we need to add structured discount to each item
				jsonData.itemList = await Promise.all(
					jsonData.itemList.map(async (item) => {
						const product = await db.collection("products").findOne({ code: item.productName });
						item.structuredDiscount = product?.discount || 0;
						return item;
					})
				);
				// Return the parsed JSON data
				return jsonData;
			} catch (error) {
				logger.error("Error parsing Excel file:", error);
				return {
					ok: false,
					message: error.message,
				};
			}
		},
		/**
		 * Converts an Excel file to JSON format with additional processing for warehouse allocation
		 * and product details enrichment.
		 * This function reads an Excel file, extracts product and quantity details, fetches additional
		 * product information from the database, and allocates warehouses based on product type and stock availability.
		 * @param {Buffer} fileBuffer - The buffer containing the Excel file data
		 * @returns {Object} The parsed JSON data or an error object
		 */
		excelToJsonV2: async (fileBuffer) => {
			// Create a new instance of ExcelJS Workbook to handle the Excel file
			const workbook = new ExcelJS.Workbook();

			// Load the Excel file from the provided buffer
			await workbook.xlsx.load(fileBuffer);

			// Get the first worksheet in the Excel file
			const worksheet = workbook.getWorksheet(1); // 1 refers to the first sheet

			// Initialize an object to store the extracted data
			const jsonData = { itemList: [], invalidSKUs: [] };
			let lineItemHeader = {}; // To store the column headers for line items
			let hasData = false; // Flag to check if the Excel has any data

			// Iterate over rows in the worksheet to extract data
			for (let rowNumber = 1; rowNumber <= worksheet.rowCount; rowNumber++) {
				let row = worksheet.getRow(rowNumber);
				const rowData = {}; // Object to store data for each row

				// Extract the header row (first row) to map column names
				if (rowNumber === 1) {
					for (let colNumber = 1; colNumber <= row.cellCount; colNumber++) {
						const headerValue = row.getCell(colNumber).value;
						lineItemHeader[colNumber] = typeof headerValue === "string" ? headerValue.trim() : headerValue;
					}

					// Check if the header row has expected columns
					if (!Object.values(lineItemHeader).includes("SKU")) {
						return {
							ok: false,
							message: "Excel file is in wrong format. SKU column is missing.",
						};
					}
				}

				// Process the data rows starting from the second row
				if (rowNumber >= 2) {
					for (let colNumber = 1; colNumber <= row.cellCount; colNumber++) {
						const cell = row.getCell(colNumber);

						// Map the cell values to the corresponding fields based on the header
						if (lineItemHeader[colNumber] === "SKU") {
							rowData["productName"] = cell.value?.trim(); // Extract SKU (product code)
							if (rowData["productName"]) {
								hasData = true;
							}
						}
						if (lineItemHeader[colNumber] === "Quantity") {
							rowData.quantity = cell.value; // Extract quantity
						}
						if (lineItemHeader[colNumber] === "Discount %") {
							rowData.unstructuredDiscount = cell.value; // Extract discount percentage
						}
					}

					// Only add rows that have a product name (SKU)
					if (rowData["productName"]) {
						rowData.lineNumber = rowNumber; // Store the line number for error reporting
						jsonData.itemList.push(rowData); // Add the processed row data to the item list
					}
				}
			}

			// Check if the Excel file has any valid data
			if (!hasData || jsonData.itemList.length === 0) {
				return {
					ok: false,
					message: "Excel file doesn't contain any data or is in wrong format.",
				};
			}

			// Fetch product details from the database for the extracted SKUs
			const db = await Services.MongoHelper.getDatabaseConnection();
			const products = await db
				.collection("products")
				.find({ code: { $in: jsonData.itemList.map((item) => item.productName) } })
				.toArray();

			// Track valid items and invalid SKUs
			const validItems = [];

			// Enrich each item in the item list with additional product details and allocate warehouses
			jsonData.itemList.forEach((item) => {
				const product = products.find((product) => product.code === item.productName);
				if (product && product.warehouses) {
					item.warehouse_ids = []; // Initialize an array to store allocated warehouse IDs
					let remainingQuantity = item.quantity; // Track the remaining quantity to allocate
					item.category = product.category; // Add product category
					item.subCategory = product.subCategory; // Add product sub-category
					item.structuredDiscount = product.discount || 0; // Add structured discount if available

					// Allocate warehouses based on product type
					if (product.productType === "MTS") {
						// For "MTS" (Make-to-Stock) products, allocate stock from specific warehouses

						// Check "1001-Display" warehouse for stock
						const displayWarehouse = product.warehouses.find(
							(warehouse) => warehouse.warehouse_name === "1001-Display"
						);
						if (displayWarehouse && remainingQuantity > 0) {
							const allocatedQuantity = Math.min(remainingQuantity, displayWarehouse.warehouse_stock);
							if (allocatedQuantity > 0) {
								item.warehouse_ids.push(displayWarehouse.warehouse_id); // Add warehouse ID
								remainingQuantity -= allocatedQuantity; // Reduce remaining quantity
							}
						}

						// Check "1001-9504" warehouse for stock
						const warehouse9504 = product.warehouses.find(
							(warehouse) => warehouse.warehouse_name === "1001-9504"
						);
						if (warehouse9504 && remainingQuantity > 0) {
							const allocatedQuantity = Math.min(remainingQuantity, warehouse9504.warehouse_stock);
							if (allocatedQuantity > 0) {
								item.warehouse_ids.push(warehouse9504.warehouse_id); // Add warehouse ID
								remainingQuantity -= allocatedQuantity; // Reduce remaining quantity
							}
						}

						// Check "1001-16A8" warehouse for stock
						const warehouse16A8 = product.warehouses.find(
							(warehouse) => warehouse.warehouse_name === "1001-16A8"
						);
						if (warehouse16A8 && remainingQuantity > 0) {
							item.warehouse_ids.push(warehouse16A8.warehouse_id); // Add warehouse ID
						}
					}
					if (product.productType === "MTO") {
						// For "MTO" (Make-to-Order) products, allocate a specific warehouse based on environment
						config.env === "staging"
							? item.warehouse_ids.push("1846261000002306490") // Staging warehouse ID
							: item.warehouse_ids.push("2026911000000252153"); // Production warehouse ID
					}

					// Add to valid items
					validItems.push(item);
				} else {
					// Add to invalid SKUs with line number for reporting
					jsonData.invalidSKUs.push({
						lineNumber: item.lineNumber,
						sku: item.productName,
					});
				}
			});

			// Update the itemList to contain only valid items
			jsonData.itemList = validItems;

			// Check if all SKUs were invalid
			if (jsonData.itemList.length === 0 && jsonData.invalidSKUs.length > 0) {
				const skuErrors = jsonData.invalidSKUs
					.map((item) => `Line ${item.lineNumber}: SKU "${item.sku}" not found`)
					.join(", ");
				return {
					ok: false,
					message: `No valid SKUs found in the Excel file. ${skuErrors}`,
				};
			}

			// If we have some invalid SKUs but also valid ones, include warning message
			if (jsonData.invalidSKUs.length > 0) {
				const skuErrors = jsonData.invalidSKUs
					.map((item) => `Line ${item.lineNumber}: SKU "${item.sku}" not found`)
					.join(", ");
				jsonData.warning = `Some SKUs could not be recognized. ${skuErrors}`;
			}

			// Return the final JSON data with enriched details and ok: true status
			return {
				ok: true,
				data: jsonData,
				message:
					jsonData.invalidSKUs.length > 0
						? `Successfully processed ${jsonData.itemList.length} SKUs with ${jsonData.invalidSKUs.length} invalid SKUs`
						: `Successfully processed all ${jsonData.itemList.length} SKUs`,
			};
		},

		/**
		 * Retrieves sales order details for a specific architect with pagination.
		 * @param {Object} params - The parameters for fetching sales orders
		 * @param {string} params.architectCRMID - The CRM ID of the architect
		 * @param {number} params.pageNo - The page number for pagination
		 * @param {number} params.pageSize - The number of records per page
		 * @returns {Object} The sales order details and total count
		 */
		getSalesOrderDetailsForArchitect: async ({ architectCRMID, pageNo, pageSize }) => {
			pageNo = pageNo - 1;
			pageSize = Number(pageSize);
			const db = await Services.MongoHelper.getDatabaseConnection();
			const salesOrders = await db
				.collection("salesOrders")
				.find({
					"custom_field_hash.cf_architectcrmid": architectCRMID,
				})
				.skip(pageNo * pageSize)
				.limit(pageSize)
				.toArray();

			const salesOrderCount = await db.collection("salesOrders").countDocuments({
				"custom_field_hash.cf_architectcrmid": architectCRMID,
			});
			return { salesOrderCount, salesOrders };
		},

		updatePdfUrlAndStatus: async ({ status, fileUrl, quoteCRMID, disabled = false }) => {
			try {
				// Validate required parameters
				if (!quoteCRMID || !status || !fileUrl) {
					return { ok: false, error: "Missing required parameters" };
				}

				const db = await Services.MongoHelper.getDatabaseConnection();

				// Build update object based on disabled flag
				let updateFields;
				if (disabled) {
					updateFields = {
						uploadedFileDisabled: {
							status,
							fileUrl
						}
					};
				} else {
					updateFields = {
						uploadedFile: {
							status,
							fileUrl
						}
					};
				}

				const result = await db.collection("quotes").updateOne({ quoteCRMID }, { $set: updateFields });

				if (result.matchedCount === 0) {
					return { ok: false, error: "Quote not found" };
				}

				return { ok: true };
			} catch (error) {
				logger.error("Error in updatePdfUrlAndStatus:", error);
				return { ok: false, error: error.message };
			}
		},
	};
};
