/**
 * UserAnalytics Controller
 * Provides endpoints for user-level analytics
 */

const { logger } = require("../utils");
const { Parser } = require('json2csv');

module.exports.routes = function ({ Services, config }) {
    return {
        /**
         * Get User Analytics
         * This route provides user-level analytics data
         */
        "GET /": {
            version: "v1.0",
            localMiddlewares: ["isLoggedIn"],
            handler: async function (req, res) {
                try {
                    const { startDate, endDate } = req.query;

                    // Parse date parameters if provided
                    const options = {};
                    if (startDate) {
                        options.startDate = new Date(startDate);
                    }
                    if (endDate) {
                        options.endDate = new Date(endDate);
                    }

                    const data = await Services.UserAnalytics.getUserAnalytics(options);
                    if (data.ok) {
                        return res.json({ ok: true, data: data.data });
                    }
                    res.status(500).json({ ok: false, message: data.message });
                } catch (e) {
                    res.status(500).json({ ok: false, message: e.message });
                    logger.error(e);
                }
            },
        },

        /**
         * Export User Analytics as CSV
         * This route provides user-level analytics data in CSV format
         */
        "GET /export": {
            version: "v1.0",
            localMiddlewares: ["isLoggedIn"],
            handler: async function (req, res) {
                try {
                    const { startDate, endDate } = req.query;

                    // Parse date parameters if provided
                    const options = {};
                    if (startDate) {
                        options.startDate = new Date(startDate);
                    }
                    if (endDate) {
                        options.endDate = new Date(endDate);
                    }

                    const data = await Services.UserAnalytics.getUserAnalytics(options);
                    if (!data.ok) {
                        return res.status(500).json({ ok: false, message: data.message });
                    }

                    // Define fields for CSV
                    const fields = [
                        { label: 'Name', value: 'name' },
                        { label: 'Employee Code', value: 'pcode' },
                        { label: 'Login Count', value: 'loginCount' },
                        { label: 'Customers Created', value: 'customersCreated' },
                        { label: 'Quotes Created', value: 'quotesCreated' },
                        { label: 'Total Quote Value', value: 'totalQuoteValue' },
                        { label: 'Sales Orders Created', value: 'salesOrdersCreated' },
                        { label: 'Total Sales Order Value', value: 'totalSalesOrderValue' }
                    ];

                    // Create CSV parser
                    const json2csvParser = new Parser({ fields });
                    const csv = json2csvParser.parse(data.data);

                    // Set headers for CSV download
                    res.setHeader('Content-Type', 'text/csv');
                    res.setHeader('Content-Disposition', 'attachment; filename=user-analytics.csv');

                    // Send CSV data
                    return res.send(csv);
                } catch (e) {
                    res.status(500).json({ ok: false, message: e.message });
                    logger.error(e);
                }
            },
        },

        /**
         * Get User Analytics for a specific user
         * This route provides user-level analytics data for a specific user
         */
        "GET /:employeecode": {
            version: "v1.0",
            localMiddlewares: ["isLoggedIn"],
            handler: async function (req, res) {
                try {
                    const { employeecode } = req.params;
                    const { startDate, endDate } = req.query;

                    // Parse date parameters if provided
                    const options = {
                        employeecode
                    };

                    if (startDate) {
                        options.startDate = new Date(startDate);
                    }
                    if (endDate) {
                        options.endDate = new Date(endDate);
                    }

                    const data = await Services.UserAnalytics.getUserAnalyticsByEmployeeCode(options);
                    if (data.ok) {
                        return res.json({ ok: true, data: data.data });
                    }
                    res.status(500).json({ ok: false, message: data.message });
                } catch (e) {
                    res.status(500).json({ ok: false, message: e.message });
                    logger.error(e);
                }
            },
        },
    };
};
